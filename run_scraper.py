#!/usr/bin/env python3
"""
Script to run Trump tweet scraping
Usage: python run_scraper.py [--months 6] [--max-posts 1000] [--output-dir data/raw]
"""

import argparse
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_collection.trump_scraper import TrumpTweetScraper


def main():
    parser = argparse.ArgumentParser(description='Scrape Trump tweets from Truth Social')
    parser.add_argument('--months', type=int, default=6, 
                       help='Number of months to go back (default: 6)')
    parser.add_argument('--max-posts', type=int, default=None,
                       help='Maximum number of posts to scrape (default: no limit)')
    parser.add_argument('--output-dir', type=str, default='data/raw',
                       help='Output directory for scraped data (default: data/raw)')
    parser.add_argument('--no-json', action='store_true',
                       help='Skip saving JSON file')
    parser.add_argument('--no-csv', action='store_true',
                       help='Skip saving CSV file')
    
    args = parser.parse_args()
    
    print(f"Starting Trump tweet scraper...")
    print(f"Months back: {args.months}")
    print(f"Max posts: {args.max_posts or 'No limit'}")
    print(f"Output directory: {args.output_dir}")
    
    try:
        scraper = TrumpTweetScraper(output_dir=args.output_dir)
        
        results = scraper.run_scraping(
            months_back=args.months,
            max_posts=args.max_posts,
            save_json=not args.no_json,
            save_csv=not args.no_csv
        )
        
        if results['success']:
            print(f"\n✅ Scraping completed successfully!")
            print(f"📊 Posts scraped: {results['posts_count']}")
            
            if 'date_range' in results:
                start_date = results['date_range']['start']
                end_date = results['date_range']['end']
                print(f"📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            if 'json_file' in results:
                print(f"💾 JSON file: {results['json_file']}")
            if 'csv_file' in results:
                print(f"💾 CSV file: {results['csv_file']}")
        else:
            print(f"\n❌ Scraping failed: {results.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  Scraping interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during scraping: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 