import pandas as pd
import json
import re
import os
from datetime import datetime
from typing import List, Dict, Set, Tuple
import logging

from keyword_lists import (
    ALL_MARKET_KEYWORDS, ALL_COMPANIES, SECTOR_KEYWORDS,
    ECONOMIC_TERMS, TECH_COMPANIES, FINANCIAL_COMPANIES, 
    ENERGY_COMPANIES, HEALTHCARE_COMPANIES, INDUSTRIAL_COMPANIES,
    CONSUMER_COMPANIES, COMMODITIES
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/market_filter.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MarketPostFilter:
    """Filters posts for market relevance and adds metadata"""
    
    def __init__(self, input_dir: str = "data/raw", output_dir: str = "data/processed"):
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        # File paths
        self.input_csv = os.path.join(input_dir, "trump_posts_incremental.csv")
        self.input_json = os.path.join(input_dir, "trump_posts_incremental.json")
        self.output_csv = os.path.join(output_dir, "market_relevant_posts.csv")
        self.output_json = os.path.join(output_dir, "market_relevant_posts.json")
        
        # Threshold for market relevance (0-100)
        self.relevance_threshold = 25  # Adjustable threshold
        
        # Pre-compile regex patterns for efficiency
        self._compile_patterns()
        
        logger.info(f"Initialized Market Post Filter")
        logger.info(f"Relevance threshold: {self.relevance_threshold}")
    
    def _compile_patterns(self):
        """Pre-compile regex patterns for keyword matching"""
        # Create case-insensitive patterns for all keywords
        self.keyword_patterns = {}
        for keyword in ALL_MARKET_KEYWORDS:
            # Escape special regex characters and create word boundary pattern
            escaped_keyword = re.escape(keyword.lower())
            pattern = rf'\b{escaped_keyword}\b'
            self.keyword_patterns[keyword] = re.compile(pattern, re.IGNORECASE)
        
        # Company name patterns (more flexible matching)
        self.company_patterns = {}
        for company in ALL_COMPANIES:
            escaped_company = re.escape(company.lower())
            pattern = rf'\b{escaped_company}\b'
            self.company_patterns[company] = re.compile(pattern, re.IGNORECASE)
    
    def _find_keyword_matches(self, text: str) -> List[str]:
        """Find all keyword matches in text"""
        matches = []
        text_lower = text.lower()
        
        for keyword, pattern in self.keyword_patterns.items():
            if pattern.search(text_lower):
                matches.append(keyword)
        
        return matches
    
    def _find_company_matches(self, text: str) -> List[str]:
        """Find all company name matches in text"""
        matches = []
        text_lower = text.lower()
        
        for company, pattern in self.company_patterns.items():
            if pattern.search(text_lower):
                matches.append(company)
        
        return matches
    
    def _identify_impacted_sectors(self, keyword_matches: List[str], company_matches: List[str]) -> List[str]:
        """Identify which sectors are impacted based on keywords and companies"""
        impacted_sectors = set()
        
        # Check each sector's keywords
        for sector, keywords in SECTOR_KEYWORDS.items():
            sector_keywords = [k.lower() for k in keywords]
            for match in keyword_matches:
                if match.lower() in sector_keywords:
                    impacted_sectors.add(sector)
        
        # Add sectors based on company mentions
        company_sectors = {
            "technology": TECH_COMPANIES,
            "financial": FINANCIAL_COMPANIES,
            "energy": ENERGY_COMPANIES,
            "healthcare": HEALTHCARE_COMPANIES,
            "industrial": INDUSTRIAL_COMPANIES,
            "consumer": CONSUMER_COMPANIES
        }
        
        for sector, companies in company_sectors.items():
            sector_companies = [c.lower() for c in companies]
            for company in company_matches:
                if company.lower() in sector_companies:
                    impacted_sectors.add(sector)
        
        return list(impacted_sectors)
    
    def _calculate_relevance_score(self, keyword_matches: List[str], company_matches: List[str], 
                                 impacted_sectors: List[str]) -> int:
        """Calculate market relevance score (0-100)"""
        score = 0
        
        # Base score from keyword matches
        score += len(keyword_matches) * 5  # 5 points per keyword
        
        # Bonus for company mentions (more specific)
        score += len(company_matches) * 10  # 10 points per company
        
        # Bonus for multiple sectors impacted
        score += len(impacted_sectors) * 8  # 8 points per sector
        
        # Bonus for economic/policy terms (higher impact)
        economic_matches = [k for k in keyword_matches if k.lower() in [term.lower() for term in ECONOMIC_TERMS]]
        score += len(economic_matches) * 3  # Extra 3 points per economic term
        
        # Cap at 100
        return min(score, 100)
    
    def _categorize_post_type(self, keyword_matches: List[str], company_matches: List[str], 
                            impacted_sectors: List[str]) -> str:
        """Categorize the type of market-relevant post"""
        if company_matches:
            if len(company_matches) == 1:
                return f"company_mention_{company_matches[0].lower().replace(' ', '_')}"
            else:
                return "multiple_companies"
        
        # Check for specific categories
        economic_terms = [k for k in keyword_matches if k.lower() in [term.lower() for term in ECONOMIC_TERMS]]
        if economic_terms:
            if any(term in ['tariff', 'tariffs', 'trade'] for term in economic_terms):
                return "trade_policy"
            elif any(term in ['tax', 'taxes', 'taxation'] for term in economic_terms):
                return "tax_policy"
            elif any(term in ['interest rate', 'fed', 'federal reserve'] for term in economic_terms):
                return "monetary_policy"
            else:
                return "economic_commentary"
        
        if any(k in ['stock market', 'nasdaq', 's&p 500', 'dow'] for k in keyword_matches):
            return "market_commentary"
        
        if any(k in ['crypto', 'bitcoin', 'ethereum'] for k in keyword_matches):
            return "cryptocurrency"
        
        if len(impacted_sectors) > 1:
            return "multi_sector"
        
        return "market_relevant"
    
    def _process_post(self, post: Dict) -> Dict:
        """Process a single post and add market relevance metadata"""
        text = post.get('text', '')
        
        # Find matches
        keyword_matches = self._find_keyword_matches(text)
        company_matches = self._find_company_matches(text)
        impacted_sectors = self._identify_impacted_sectors(keyword_matches, company_matches)
        
        # Calculate relevance score
        relevance_score = self._calculate_relevance_score(keyword_matches, company_matches, impacted_sectors)
        
        # Categorize post type
        post_type = self._categorize_post_type(keyword_matches, company_matches, impacted_sectors)
        
        # Create enhanced post with metadata
        enhanced_post = post.copy()
        enhanced_post.update({
            'market_relevance_score': relevance_score,
            'keyword_matches': keyword_matches,
            'mentioned_companies': company_matches,
            'impacted_sectors': impacted_sectors,
            'post_type': post_type,
            'is_market_relevant': relevance_score >= self.relevance_threshold
        })
        
        return enhanced_post
    
    def filter_posts(self) -> Dict:
        """Filter posts for market relevance"""
        logger.info("Starting market relevance filtering...")
        
        # Read input data
        try:
            df = pd.read_csv(self.input_csv)
            logger.info(f"Loaded {len(df)} posts from CSV")
        except Exception as e:
            logger.error(f"Failed to read CSV: {e}")
            return {'success': False, 'error': str(e)}
        
        # Process each post
        enhanced_posts = []
        market_relevant_posts = []
        
        for idx, row in df.iterrows():
            post = row.to_dict()
            enhanced_post = self._process_post(post)
            enhanced_posts.append(enhanced_post)
            
            if enhanced_post['is_market_relevant']:
                market_relevant_posts.append(enhanced_post)
        
        logger.info(f"Processed {len(enhanced_posts)} total posts")
        logger.info(f"Found {len(market_relevant_posts)} market-relevant posts")
        
        # Save results
        try:
            # Save all enhanced posts (for potential future use)
            enhanced_df = pd.DataFrame(enhanced_posts)
            enhanced_csv = os.path.join(self.output_dir, "all_posts_enhanced.csv")
            enhanced_df.to_csv(enhanced_csv, index=False, encoding='utf-8')
            
            # Save market-relevant posts only
            if market_relevant_posts:
                market_df = pd.DataFrame(market_relevant_posts)
                market_df.to_csv(self.output_csv, index=False, encoding='utf-8')
                
                # Save as JSON
                with open(self.output_json, 'w', encoding='utf-8') as f:
                    json.dump(market_relevant_posts, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Saved {len(market_relevant_posts)} market-relevant posts")
                logger.info(f"CSV: {self.output_csv}")
                logger.info(f"JSON: {self.output_json}")
            else:
                logger.warning("No market-relevant posts found!")
            
            # Generate summary statistics
            summary = self._generate_summary(enhanced_posts, market_relevant_posts)
            
            return {
                'success': True,
                'total_posts': len(enhanced_posts),
                'market_relevant_posts': len(market_relevant_posts),
                'relevance_threshold': self.relevance_threshold,
                'summary': summary,
                'files': {
                    'enhanced_csv': enhanced_csv,
                    'market_csv': self.output_csv,
                    'market_json': self.output_json
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to save results: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_summary(self, all_posts: List[Dict], market_posts: List[Dict]) -> Dict:
        """Generate summary statistics"""
        # Score distribution
        scores = [post['market_relevance_score'] for post in all_posts]
        
        # Sector distribution
        sector_counts = {}
        for post in market_posts:
            for sector in post['impacted_sectors']:
                sector_counts[sector] = sector_counts.get(sector, 0) + 1
        
        # Post type distribution
        type_counts = {}
        for post in market_posts:
            post_type = post['post_type']
            type_counts[post_type] = type_counts.get(post_type, 0) + 1
        
        # Company mentions
        company_counts = {}
        for post in market_posts:
            for company in post['mentioned_companies']:
                company_counts[company] = company_counts.get(company, 0) + 1
        
        return {
            'score_distribution': {
                'min': min(scores),
                'max': max(scores),
                'mean': sum(scores) / len(scores),
                'median': sorted(scores)[len(scores)//2]
            },
            'sector_distribution': dict(sorted(sector_counts.items(), key=lambda x: x[1], reverse=True)),
            'post_type_distribution': dict(sorted(type_counts.items(), key=lambda x: x[1], reverse=True)),
            'top_companies': dict(sorted(company_counts.items(), key=lambda x: x[1], reverse=True)[:10])
        }


def main():
    """Main function for market post filtering"""
    filter = MarketPostFilter()
    
    # Run filtering
    results = filter.filter_posts()
    
    if results['success']:
        print(f"\n✅ Market filtering completed!")
        print(f"📊 Total posts processed: {results['total_posts']}")
        print(f"🎯 Market-relevant posts: {results['market_relevant_posts']}")
        print(f"📈 Relevance threshold: {results['relevance_threshold']}")
        
        # Print summary
        summary = results['summary']
        print(f"\n📋 Summary Statistics:")
        print(f"Score range: {summary['score_distribution']['min']}-{summary['score_distribution']['max']}")
        print(f"Average score: {summary['score_distribution']['mean']:.1f}")
        
        print(f"\n🏭 Top Sectors:")
        for sector, count in list(summary['sector_distribution'].items())[:5]:
            print(f"  {sector}: {count} posts")
        
        print(f"\n📝 Top Post Types:")
        for post_type, count in list(summary['post_type_distribution'].items())[:5]:
            print(f"  {post_type}: {count} posts")
        
        print(f"\n🏢 Top Companies:")
        for company, count in list(summary['top_companies'].items())[:5]:
            print(f"  {company}: {count} mentions")
        
        print(f"\n📁 Output files:")
        print(f"  Market-relevant CSV: {results['files']['market_csv']}")
        print(f"  Market-relevant JSON: {results['files']['market_json']}")
        print(f"  Enhanced CSV (all posts): {results['files']['enhanced_csv']}")
        
    else:
        print(f"\n❌ Filtering failed: {results.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main() 