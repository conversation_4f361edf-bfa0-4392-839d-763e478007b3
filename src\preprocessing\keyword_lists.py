"""
Keyword lists for market impact filtering
Organized by categories for comprehensive market relevance detection
"""

# Economic and Policy Terms
ECONOMIC_TERMS = [
    # Trade and Tariffs
    "tariff", "tariffs", "trade", "trade war", "trade deficit", "trade surplus",
    "import", "export", "duty", "duties", "protectionism", "free trade",
    
    # Economic Indicators
    "inflation", "deflation", "recession", "depression", "growth", "gdp",
    "unemployment", "employment", "jobs", "wages", "salary", "income",
    "consumer price", "cpi", "ppi", "retail sales", "manufacturing",
    
    # Monetary Policy
    "interest rate", "federal reserve", "fed", "monetary policy", "quantitative easing",
    "money supply", "currency", "dollar", "euro", "yen", "yuan", "exchange rate",
    "central bank", "banking", "credit", "lending", "borrowing",
    
    # Fiscal Policy
    "tax", "taxes", "taxation", "tax cut", "tax increase", "tax reform",
    "budget", "deficit", "surplus", "debt", "spending", "stimulus",
    "infrastructure", "government spending", "fiscal policy",
    
    # Market Terms
    "stock market", "stock exchange", "wall street", "bull market", "bear market",
    "market crash", "market rally", "market correction", "volatility",
    "trading", "investor", "investment", "portfolio", "asset", "assets",
    "securities", "equity", "equities", "bond", "bonds", "dividend",
    "earnings", "revenue", "profit", "loss", "quarterly", "annual",
    
    # Business Terms
    "business", "corporation", "company", "companies", "industry", "sector",
    "merger", "acquisition", "ipo", "initial public offering", "bankruptcy",
    "restructuring", "layoff", "layoffs", "hiring", "expansion", "contraction"
]

# Major Stock Indices and Markets
MARKET_INDICES = [
    "s&p 500", "sp500", "sp 500", "nasdaq", "dow jones", "dow", "russell 2000",
    "wilshire 5000", "nyse", "new york stock exchange", "amex", "otc",
    "over the counter", "futures", "options", "derivatives"
]

# Technology Sector
TECH_COMPANIES = [
    "apple", "microsoft", "google", "alphabet", "amazon", "meta", "facebook",
    "netflix", "tesla", "nvidia", "amd", "intel", "qualcomm", "broadcom",
    "oracle", "salesforce", "adobe", "cisco", "ibm", "dell", "hp", "hewlett packard",
    "twitter", "x", "snapchat", "tiktok", "uber", "lyft", "airbnb", "zoom",
    "palantir", "snowflake", "datadog", "mongodb", "shopify", "square", "block",
    "paypal", "stripe", "robinhood", "coinbase", "binance", "microstrategy"
]

TECH_TERMS = [
    "artificial intelligence", "ai", "machine learning", "ml", "deep learning",
    "blockchain", "cryptocurrency", "crypto", "bitcoin", "ethereum", "digital currency",
    "cloud computing", "saas", "software as a service", "cybersecurity", "cyber security",
    "data center", "datacenter", "semiconductor", "chip", "chips", "processor",
    "electric vehicle", "ev", "autonomous", "self driving", "autonomous vehicle",
    "social media", "platform", "app", "application", "software", "hardware",
    "internet", "web", "digital", "technology", "tech", "innovation", "startup"
]

# Financial Sector
FINANCIAL_COMPANIES = [
    "jpmorgan", "jp morgan", "chase", "bank of america", "wells fargo", "citigroup",
    "goldman sachs", "morgan stanley", "blackrock", "vanguard", "fidelity",
    "charles schwab", "td ameritrade", "etrade", "robinhood", "coinbase",
    "paypal", "visa", "mastercard", "american express", "amex", "discover"
]

FINANCIAL_TERMS = [
    "bank", "banking", "financial", "finance", "investment", "investor",
    "portfolio", "asset management", "wealth management", "retirement",
    "401k", "ira", "pension", "insurance", "underwriting", "lending",
    "credit", "debit", "payment", "transaction", "merchant", "acquirer"
]

# Energy Sector
ENERGY_COMPANIES = [
    "exxon", "exxonmobil", "chevron", "conocophillips", "phillips 66",
    "valero", "marathon petroleum", "occidental petroleum", "oxy",
    "schlumberger", "halliburton", "baker hughes", "kinder morgan",
    "energy transfer", "enterprise products", "oneok", "williams companies"
]

ENERGY_TERMS = [
    "oil", "gasoline", "petroleum", "natural gas", "lng", "crude oil",
    "refinery", "refining", "pipeline", "drilling", "fracking", "shale",
    "renewable energy", "solar", "wind", "nuclear", "coal", "electricity",
    "energy", "power", "utility", "utilities", "grid", "battery", "batteries"
]

# Healthcare Sector
HEALTHCARE_COMPANIES = [
    "johnson & johnson", "pfizer", "moderna", "biontech", "astrazeneca",
    "merck", "eli lilly", "abbott", "medtronic", "unitedhealth", "anthem",
    "cigna", "humana", "aetna", "cvs", "walgreens", "amgen", "gilead",
    "biogen", "regeneron", "vertex", "illumina", "thermo fisher"
]

HEALTHCARE_TERMS = [
    "pharmaceutical", "pharma", "drug", "medicine", "medical", "healthcare",
    "health care", "hospital", "clinic", "doctor", "physician", "nurse",
    "insurance", "medicare", "medicaid", "vaccine", "vaccination", "treatment",
    "therapy", "clinical trial", "fda", "food and drug administration"
]

# Industrial and Manufacturing
INDUSTRIAL_COMPANIES = [
    "boeing", "lockheed martin", "raytheon", "northrop grumman", "general electric",
    "ge", "caterpillar", "deere", "john deere", "3m", "honeywell", "eaton",
    "emerson", "rockwell automation", "parker hannifin", "illinois tool works"
]

INDUSTRIAL_TERMS = [
    "manufacturing", "factory", "industrial", "machinery", "equipment",
    "aerospace", "defense", "military", "weapon", "weapons", "aircraft",
    "automotive", "car", "truck", "vehicle", "construction", "infrastructure",
    "steel", "aluminum", "copper", "metal", "metals", "mining", "mineral"
]

# Consumer and Retail
CONSUMER_COMPANIES = [
    "walmart", "costco", "home depot", "lowes", "mcdonalds",
    "starbucks", "coca cola", "pepsi", "procter & gamble", "pg", "unilever",
    "nestle", "kraft heinz", "general mills", "kellogg", "mondelez",
    "nike", "adidas", "disney", "netflix", "comcast", "verizon", "at&t"
]

CONSUMER_TERMS = [
    "retail", "consumer", "shopping", "store", "supermarket", "grocery",
    "restaurant", "fast food", "beverage", "food", "clothing", "apparel",
    "entertainment", "media", "telecom", "telecommunications", "wireless",
    "internet service", "cable", "streaming", "content", "advertising"
]

# Commodities
COMMODITIES = [
    "gold", "silver", "platinum", "palladium", "copper", "aluminum", "steel",
    "iron", "nickel", "zinc", "lead", "tin", "oil", "crude", "natural gas",
    "coal", "corn", "wheat", "soybeans", "cotton", "sugar", "coffee",
    "cocoa", "orange juice", "lumber", "rubber", "plastics"
]

# Real Estate
REAL_ESTATE_TERMS = [
    "real estate", "real estate", "property", "properties", "housing",
    "home", "house", "mortgage", "mortgages", "refinance", "refinancing",
    "construction", "builder", "developer", "reit", "real estate investment trust",
    "commercial real estate", "residential", "apartment", "condo", "condominium"
]

# Transportation and Logistics
TRANSPORTATION_TERMS = [
    "transportation", "logistics", "shipping", "freight", "trucking",
    "railroad", "railway", "airline", "airlines", "aviation", "airport",
    "port", "harbor", "maritime", "delivery", "supply chain", "warehouse",
    "distribution", "truck", "trucking", "rail", "train", "plane", "ship"
]

# All keywords combined for easy access
ALL_MARKET_KEYWORDS = (
    ECONOMIC_TERMS + MARKET_INDICES + TECH_TERMS + FINANCIAL_TERMS + 
    ENERGY_TERMS + HEALTHCARE_TERMS + INDUSTRIAL_TERMS + CONSUMER_TERMS + 
    COMMODITIES + REAL_ESTATE_TERMS + TRANSPORTATION_TERMS
)

# All company names combined
ALL_COMPANIES = (
    TECH_COMPANIES + FINANCIAL_COMPANIES + ENERGY_COMPANIES + 
    HEALTHCARE_COMPANIES + INDUSTRIAL_COMPANIES + CONSUMER_COMPANIES
)

# Sector mappings for categorization
SECTOR_KEYWORDS = {
    "economic_policy": ECONOMIC_TERMS + MARKET_INDICES,
    "technology": TECH_COMPANIES + TECH_TERMS,
    "financial": FINANCIAL_COMPANIES + FINANCIAL_TERMS,
    "energy": ENERGY_COMPANIES + ENERGY_TERMS,
    "healthcare": HEALTHCARE_COMPANIES + HEALTHCARE_TERMS,
    "industrial": INDUSTRIAL_COMPANIES + INDUSTRIAL_TERMS,
    "consumer": CONSUMER_COMPANIES + CONSUMER_TERMS,
    "commodities": COMMODITIES,
    "real_estate": REAL_ESTATE_TERMS,
    "transportation": TRANSPORTATION_TERMS
} 