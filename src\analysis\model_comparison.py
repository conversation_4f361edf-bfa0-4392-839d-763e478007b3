#!/usr/bin/env python3
"""
Model Comparison Framework for Sentiment Analysis
Compares FinBERT and Llama 3.1 sentiment analysis results on market-relevant posts
Provides detailed comparison metrics, agreement analysis, and performance evaluation
"""

import pandas as pd
import numpy as np
import json
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from pathlib import Path
from scipy.stats import pearsonr, spearmanr
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/model_comparison.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelComparisonFramework:
    """
    Framework for comparing sentiment analysis results between different models
    """
    
    def __init__(self, finbert_results_path: str, llama3_results_path: str, output_dir: str = "data/comparison"):
        """
        Initialize comparison framework
        
        Args:
            finbert_results_path: Path to FinBERT results CSV
            llama3_results_path: Path to Llama 3.1 results CSV
            output_dir: Output directory for comparison results
        """
        self.finbert_path = finbert_results_path
        self.llama3_path = llama3_results_path
        self.output_dir = output_dir
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        logger.info("Initialized Model Comparison Framework")
        logger.info(f"FinBERT results: {finbert_results_path}")
        logger.info(f"Llama 3.1 results: {llama3_results_path}")
        logger.info(f"Output directory: {output_dir}")
    
    def load_results(self):
        """Load both model results and align them"""
        try:
            # Load FinBERT results
            logger.info("Loading FinBERT results...")
            self.df_finbert = pd.read_csv(self.finbert_path)
            logger.info(f"Loaded {len(self.df_finbert)} FinBERT results")
            
            # Load Llama 3.1 results
            logger.info("Loading Llama 3.1 results...")
            self.df_llama3 = pd.read_csv(self.llama3_path)
            logger.info(f"Loaded {len(self.df_llama3)} Llama 3.1 results")
            
            # Align datasets by post ID
            self.df_comparison = self._align_datasets()
            logger.info(f"Aligned {len(self.df_comparison)} posts for comparison")
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading results: {e}")
            return False
    
    def _align_datasets(self):
        """Align FinBERT and Llama 3.1 datasets by post ID"""
        # Merge on post ID to ensure we're comparing the same posts
        df_merged = pd.merge(
            self.df_finbert,
            self.df_llama3[['id', 'primary_sentiment', 'primary_score', 'positive_score', 
                           'negative_score', 'neutral_score', 'processing_time']],
            on='id',
            suffixes=('_finbert', '_llama3')
        )
        
        return df_merged
    
    def calculate_agreement_metrics(self):
        """Calculate agreement metrics between models"""
        logger.info("Calculating agreement metrics...")
        
        # Label agreement
        label_agreement = (self.df_comparison['primary_sentiment_finbert'] == 
                          self.df_comparison['primary_sentiment_llama3']).mean()
        
        # Score correlations
        pos_corr = pearsonr(self.df_comparison['positive_score_finbert'], 
                           self.df_comparison['positive_score_llama3'])[0]
        neg_corr = pearsonr(self.df_comparison['negative_score_finbert'], 
                           self.df_comparison['negative_score_llama3'])[0]
        neut_corr = pearsonr(self.df_comparison['neutral_score_finbert'], 
                            self.df_comparison['neutral_score_llama3'])[0]
        
        # Spearman correlations (rank-based)
        pos_spearman = spearmanr(self.df_comparison['positive_score_finbert'], 
                                self.df_comparison['positive_score_llama3'])[0]
        neg_spearman = spearmanr(self.df_comparison['negative_score_finbert'], 
                                self.df_comparison['negative_score_llama3'])[0]
        neut_spearman = spearmanr(self.df_comparison['neutral_score_finbert'], 
                                 self.df_comparison['neutral_score_llama3'])[0]
        
        # Create confusion matrix
        confusion_matrix_data = confusion_matrix(
            self.df_comparison['primary_sentiment_finbert'],
            self.df_comparison['primary_sentiment_llama3'],
            labels=['positive', 'negative', 'neutral']
        )
        
        # Classification report
        class_report = classification_report(
            self.df_comparison['primary_sentiment_finbert'],
            self.df_comparison['primary_sentiment_llama3'],
            labels=['positive', 'negative', 'neutral'],
            output_dict=True
        )
        
        self.agreement_metrics = {
            'label_agreement': label_agreement,
            'score_correlations': {
                'positive': {'pearson': pos_corr, 'spearman': pos_spearman},
                'negative': {'pearson': neg_corr, 'spearman': neg_spearman},
                'neutral': {'pearson': neut_corr, 'spearman': neut_spearman}
            },
            'confusion_matrix': confusion_matrix_data,
            'classification_report': class_report
        }
        
        logger.info(f"Label agreement: {label_agreement:.3f}")
        logger.info(f"Positive score correlation: {pos_corr:.3f}")
        logger.info(f"Negative score correlation: {neg_corr:.3f}")
        logger.info(f"Neutral score correlation: {neut_corr:.3f}")
        
        return self.agreement_metrics
    
    def analyze_disagreements(self):
        """Analyze cases where models disagree"""
        logger.info("Analyzing model disagreements...")
        
        # Find disagreements
        disagreements = self.df_comparison[
            self.df_comparison['primary_sentiment_finbert'] != 
            self.df_comparison['primary_sentiment_llama3']
        ].copy()
        
        logger.info(f"Found {len(disagreements)} disagreements ({len(disagreements)/len(self.df_comparison)*100:.1f}%)")
        
        # Analyze disagreement patterns
        disagreement_patterns = {}
        for _, row in disagreements.iterrows():
            pattern = f"{row['primary_sentiment_finbert']}_to_{row['primary_sentiment_llama3']}"
            disagreement_patterns[pattern] = disagreement_patterns.get(pattern, 0) + 1
        
        # Find high-confidence disagreements
        high_conf_disagreements = disagreements[
            (disagreements['primary_score_finbert'] > 0.7) & 
            (disagreements['primary_score_llama3'] > 0.7)
        ]
        
        # Analyze by post characteristics
        disagreement_by_sector = {}
        for sector in ['technology', 'financial', 'energy', 'healthcare', 'industrial', 'consumer']:
            sector_posts = disagreements[disagreements['impacted_sectors'].str.contains(sector, na=False)]
            disagreement_by_sector[sector] = len(sector_posts)
        
        disagreement_by_type = disagreements['post_type'].value_counts().to_dict()
        
        self.disagreement_analysis = {
            'total_disagreements': len(disagreements),
            'disagreement_rate': len(disagreements) / len(self.df_comparison),
            'disagreement_patterns': disagreement_patterns,
            'high_confidence_disagreements': len(high_conf_disagreements),
            'disagreement_by_sector': disagreement_by_sector,
            'disagreement_by_post_type': disagreement_by_type,
            'disagreement_examples': disagreements[['id', 'text', 'primary_sentiment_finbert', 
                                                  'primary_sentiment_llama3', 'primary_score_finbert', 
                                                  'primary_score_llama3']].head(10).to_dict('records')
        }
        
        return self.disagreement_analysis
    
    def analyze_performance_metrics(self):
        """Analyze performance and resource usage"""
        logger.info("Analyzing performance metrics...")
        
        # Processing time comparison (if available)
        performance_metrics = {}
        
        if 'processing_time_llama3' in self.df_comparison.columns:
            avg_time_llama3 = self.df_comparison['processing_time_llama3'].mean()
            total_time_llama3 = self.df_comparison['processing_time_llama3'].sum()
            
            performance_metrics.update({
                'llama3_avg_time_per_post': avg_time_llama3,
                'llama3_total_time': total_time_llama3,
                'llama3_posts_per_minute': 60 / avg_time_llama3 if avg_time_llama3 > 0 else 0
            })
        
        # Note: FinBERT processing time not available in current data
        # but can be estimated or measured separately
        
        # Confidence distribution analysis
        confidence_analysis = {
            'finbert_confidence': {
                'mean': self.df_comparison['primary_score_finbert'].mean(),
                'std': self.df_comparison['primary_score_finbert'].std(),
                'min': self.df_comparison['primary_score_finbert'].min(),
                'max': self.df_comparison['primary_score_finbert'].max(),
                'high_confidence_count': (self.df_comparison['primary_score_finbert'] > 0.8).sum()
            },
            'llama3_confidence': {
                'mean': self.df_comparison['primary_score_llama3'].mean(),
                'std': self.df_comparison['primary_score_llama3'].std(),
                'min': self.df_comparison['primary_score_llama3'].min(),
                'max': self.df_comparison['primary_score_llama3'].max(),
                'high_confidence_count': (self.df_comparison['primary_score_llama3'] > 0.8).sum()
            }
        }
        
        self.performance_metrics = {
            **performance_metrics,
            'confidence_analysis': confidence_analysis
        }
        
        return self.performance_metrics
    
    def analyze_sector_performance(self):
        """Analyze model performance by market sector"""
        logger.info("Analyzing sector-wise performance...")
        
        sector_analysis = {}
        sectors = ['technology', 'financial', 'energy', 'healthcare', 'industrial', 'consumer']
        
        for sector in sectors:
            # Filter posts containing this sector
            sector_mask = self.df_comparison['impacted_sectors'].str.contains(sector, na=False)
            sector_posts = self.df_comparison[sector_mask]
            
            if len(sector_posts) > 0:
                # Agreement rate for this sector
                sector_agreement = (sector_posts['primary_sentiment_finbert'] == 
                                  sector_posts['primary_sentiment_llama3']).mean()
                
                # Score correlations for this sector
                pos_corr = pearsonr(sector_posts['positive_score_finbert'], 
                                   sector_posts['positive_score_llama3'])[0]
                
                # Sentiment distribution for each model
                finbert_dist = sector_posts['primary_sentiment_finbert'].value_counts(normalize=True).to_dict()
                llama3_dist = sector_posts['primary_sentiment_llama3'].value_counts(normalize=True).to_dict()
                
                sector_analysis[sector] = {
                    'post_count': len(sector_posts),
                    'agreement_rate': sector_agreement,
                    'positive_score_correlation': pos_corr,
                    'finbert_sentiment_distribution': finbert_dist,
                    'llama3_sentiment_distribution': llama3_dist
                }
        
        self.sector_analysis = sector_analysis
        return sector_analysis
    
    def create_visualizations(self):
        """Create comparison visualizations"""
        logger.info("Creating comparison visualizations...")
        
        # Set up matplotlib style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Confusion Matrix
        ax1 = plt.subplot(3, 4, 1)
        sns.heatmap(self.agreement_metrics['confusion_matrix'], 
                    annot=True, fmt='d', cmap='Blues',
                    xticklabels=['Positive', 'Negative', 'Neutral'],
                    yticklabels=['Positive', 'Negative', 'Neutral'],
                    ax=ax1)
        ax1.set_title('Model Agreement\n(FinBERT vs Llama 3.1)')
        ax1.set_xlabel('Llama 3.1 Predictions')
        ax1.set_ylabel('FinBERT Predictions')
        
        # 2. Score Correlations
        ax2 = plt.subplot(3, 4, 2)
        sentiment_scores = ['positive', 'negative', 'neutral']
        correlations = [self.agreement_metrics['score_correlations'][s]['pearson'] for s in sentiment_scores]
        bars = ax2.bar(sentiment_scores, correlations, color=['green', 'red', 'gray'])
        ax2.set_title('Score Correlations\n(Pearson)')
        ax2.set_ylabel('Correlation')
        ax2.set_ylim(0, 1)
        for bar, corr in zip(bars, correlations):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{corr:.3f}', ha='center', va='bottom')
        
        # 3. Sentiment Distribution Comparison
        ax3 = plt.subplot(3, 4, 3)
        finbert_dist = self.df_comparison['primary_sentiment_finbert'].value_counts()
        llama3_dist = self.df_comparison['primary_sentiment_llama3'].value_counts()
        
        x = np.arange(len(finbert_dist))
        width = 0.35
        ax3.bar(x - width/2, finbert_dist.values, width, label='FinBERT', alpha=0.8)
        ax3.bar(x + width/2, llama3_dist.values, width, label='Llama 3.1', alpha=0.8)
        ax3.set_title('Sentiment Distribution')
        ax3.set_xlabel('Sentiment')
        ax3.set_ylabel('Count')
        ax3.set_xticks(x)
        ax3.set_xticklabels(finbert_dist.index)
        ax3.legend()
        
        # 4. Confidence Score Distributions
        ax4 = plt.subplot(3, 4, 4)
        ax4.hist(self.df_comparison['primary_score_finbert'], bins=20, alpha=0.7, label='FinBERT', density=True)
        ax4.hist(self.df_comparison['primary_score_llama3'], bins=20, alpha=0.7, label='Llama 3.1', density=True)
        ax4.set_title('Confidence Score Distribution')
        ax4.set_xlabel('Confidence Score')
        ax4.set_ylabel('Density')
        ax4.legend()
        
        # 5. Positive Score Scatter Plot
        ax5 = plt.subplot(3, 4, 5)
        ax5.scatter(self.df_comparison['positive_score_finbert'], 
                   self.df_comparison['positive_score_llama3'], alpha=0.6)
        ax5.plot([0, 1], [0, 1], 'r--', alpha=0.8)
        ax5.set_title('Positive Scores Comparison')
        ax5.set_xlabel('FinBERT Positive Score')
        ax5.set_ylabel('Llama 3.1 Positive Score')
        
        # 6. Negative Score Scatter Plot
        ax6 = plt.subplot(3, 4, 6)
        ax6.scatter(self.df_comparison['negative_score_finbert'], 
                   self.df_comparison['negative_score_llama3'], alpha=0.6)
        ax6.plot([0, 1], [0, 1], 'r--', alpha=0.8)
        ax6.set_title('Negative Scores Comparison')
        ax6.set_xlabel('FinBERT Negative Score')
        ax6.set_ylabel('Llama 3.1 Negative Score')
        
        # 7. Disagreement Patterns
        ax7 = plt.subplot(3, 4, 7)
        patterns = list(self.disagreement_analysis['disagreement_patterns'].keys())
        counts = list(self.disagreement_analysis['disagreement_patterns'].values())
        bars = ax7.bar(range(len(patterns)), counts)
        ax7.set_title('Disagreement Patterns')
        ax7.set_xlabel('Transition Pattern')
        ax7.set_ylabel('Count')
        ax7.set_xticks(range(len(patterns)))
        ax7.set_xticklabels([p.replace('_to_', '→') for p in patterns], rotation=45)
        
        # 8. Sector Agreement Rates
        ax8 = plt.subplot(3, 4, 8)
        if hasattr(self, 'sector_analysis'):
            sectors = list(self.sector_analysis.keys())
            agreement_rates = [self.sector_analysis[s]['agreement_rate'] for s in sectors]
            bars = ax8.bar(sectors, agreement_rates)
            ax8.set_title('Agreement Rate by Sector')
            ax8.set_ylabel('Agreement Rate')
            ax8.set_xlabel('Sector')
            plt.setp(ax8.get_xticklabels(), rotation=45)
            for bar, rate in zip(bars, agreement_rates):
                ax8.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                        f'{rate:.2f}', ha='center', va='bottom')
        
        # 9. Score Difference Distribution
        ax9 = plt.subplot(3, 4, 9)
        score_diff = (self.df_comparison['positive_score_finbert'] - 
                     self.df_comparison['positive_score_llama3'])
        ax9.hist(score_diff, bins=30, alpha=0.7, edgecolor='black')
        ax9.axvline(x=0, color='red', linestyle='--', alpha=0.8)
        ax9.set_title('Positive Score Differences\n(FinBERT - Llama 3)')
        ax9.set_xlabel('Score Difference')
        ax9.set_ylabel('Count')
        
        # 10. Performance Metrics (if available)
        ax10 = plt.subplot(3, 4, 10)
        if 'llama3_avg_time_per_post' in self.performance_metrics:
            metrics = ['Llama 3.1\nAvg Time', 'FinBERT\nHigh Conf', 'Llama 3.1\nHigh Conf']
            values = [
                self.performance_metrics['llama3_avg_time_per_post'],
                self.performance_metrics['confidence_analysis']['finbert_confidence']['high_confidence_count'],
                self.performance_metrics['confidence_analysis']['llama3_confidence']['high_confidence_count']
            ]
            ax10.bar(metrics[:1], values[:1], color='orange')
            ax10_twin = ax10.twinx()
            ax10_twin.bar(metrics[1:], values[1:], color='blue', alpha=0.7)
            ax10.set_title('Performance Metrics')
            ax10.set_ylabel('Time (seconds)', color='orange')
            ax10_twin.set_ylabel('High Confidence Count', color='blue')
        
        # 11. Market Relevance Score vs Agreement
        ax11 = plt.subplot(3, 4, 11)
        agreement_by_relevance = []
        relevance_bins = [0, 30, 50, 70, 100]
        bin_labels = ['Low', 'Medium', 'High', 'Very High']
        
        for i in range(len(relevance_bins)-1):
            mask = ((self.df_comparison['market_relevance_score'] >= relevance_bins[i]) & 
                   (self.df_comparison['market_relevance_score'] < relevance_bins[i+1]))
            subset = self.df_comparison[mask]
            if len(subset) > 0:
                agreement_rate = (subset['primary_sentiment_finbert'] == 
                                subset['primary_sentiment_llama3']).mean()
                agreement_by_relevance.append(agreement_rate)
            else:
                agreement_by_relevance.append(0)
        
        bars = ax11.bar(bin_labels, agreement_by_relevance)
        ax11.set_title('Agreement by Market Relevance')
        ax11.set_ylabel('Agreement Rate')
        ax11.set_xlabel('Market Relevance Score')
        for bar, rate in zip(bars, agreement_by_relevance):
            if rate > 0:
                ax11.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                         f'{rate:.2f}', ha='center', va='bottom')
        
        # 12. Score Confidence vs Agreement
        ax12 = plt.subplot(3, 4, 12)
        # Binned analysis of how confidence relates to agreement
        confidence_bins = [0, 0.5, 0.7, 0.9, 1.0]
        conf_bin_labels = ['Low', 'Med', 'High', 'V.High']
        
        finbert_conf_agreement = []
        for i in range(len(confidence_bins)-1):
            mask = ((self.df_comparison['primary_score_finbert'] >= confidence_bins[i]) & 
                   (self.df_comparison['primary_score_finbert'] < confidence_bins[i+1]))
            subset = self.df_comparison[mask]
            if len(subset) > 0:
                agreement_rate = (subset['primary_sentiment_finbert'] == 
                                subset['primary_sentiment_llama3']).mean()
                finbert_conf_agreement.append(agreement_rate)
            else:
                finbert_conf_agreement.append(0)
        
        ax12.plot(conf_bin_labels, finbert_conf_agreement, 'o-', label='FinBERT Confidence', linewidth=2)
        ax12.set_title('Agreement vs Model Confidence')
        ax12.set_ylabel('Agreement Rate')
        ax12.set_xlabel('Confidence Level')
        ax12.legend()
        ax12.grid(True, alpha=0.3)
        
        plt.tight_layout(pad=3.0)
        
        # Save the plot
        plot_path = os.path.join(self.output_dir, "model_comparison_visualizations.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Saved visualizations to: {plot_path}")
        
        # Also save individual plots for better readability
        self._save_individual_plots()
        
        return plot_path
    
    def _save_individual_plots(self):
        """Save individual plots for better readability"""
        
        # Confusion Matrix
        plt.figure(figsize=(8, 6))
        sns.heatmap(self.agreement_metrics['confusion_matrix'], 
                    annot=True, fmt='d', cmap='Blues',
                    xticklabels=['Positive', 'Negative', 'Neutral'],
                    yticklabels=['Positive', 'Negative', 'Neutral'])
        plt.title('Model Agreement Confusion Matrix\n(FinBERT vs Llama 3.1)', fontsize=14)
        plt.xlabel('Llama 3.1 Predictions')
        plt.ylabel('FinBERT Predictions')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "confusion_matrix.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        # Score Correlations
        plt.figure(figsize=(10, 6))
        sentiment_scores = ['Positive', 'Negative', 'Neutral']
        pearson_corrs = [self.agreement_metrics['score_correlations'][s.lower()]['pearson'] 
                        for s in sentiment_scores]
        spearman_corrs = [self.agreement_metrics['score_correlations'][s.lower()]['spearman'] 
                         for s in sentiment_scores]
        
        x = np.arange(len(sentiment_scores))
        width = 0.35
        
        plt.bar(x - width/2, pearson_corrs, width, label='Pearson', alpha=0.8)
        plt.bar(x + width/2, spearman_corrs, width, label='Spearman', alpha=0.8)
        plt.title('Score Correlations Between Models', fontsize=14)
        plt.ylabel('Correlation Coefficient')
        plt.xlabel('Sentiment Type')
        plt.xticks(x, sentiment_scores)
        plt.legend()
        plt.ylim(0, 1)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "score_correlations.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("Saved individual visualization plots")
    
    def generate_comparison_report(self):
        """Generate comprehensive comparison report"""
        logger.info("Generating comparison report...")
        
        # Calculate all metrics
        agreement_metrics = self.calculate_agreement_metrics()
        disagreement_analysis = self.analyze_disagreements()
        performance_metrics = self.analyze_performance_metrics()
        sector_analysis = self.analyze_sector_performance()
        
        # Create comprehensive report
        report = {
            'comparison_metadata': {
                'analysis_timestamp': datetime.now().isoformat(),
                'total_posts_compared': len(self.df_comparison),
                'finbert_model': 'yiyanghkust/finbert-tone',
                'llama3_model': 'meta-llama/Meta-Llama-3.1-8B-Instruct',
                'comparison_framework_version': '1.0'
            },
            'agreement_analysis': agreement_metrics,
            'disagreement_analysis': disagreement_analysis,
            'performance_analysis': performance_metrics,
            'sector_analysis': sector_analysis,
            'summary_insights': self._generate_summary_insights()
        }
        
        # Save detailed results CSV
        comparison_csv = os.path.join(self.output_dir, "detailed_comparison_results.csv")
        self.df_comparison.to_csv(comparison_csv, index=False)
        logger.info(f"Saved detailed comparison CSV: {comparison_csv}")
        
        # Save report JSON
        report_json = os.path.join(self.output_dir, "comparison_report.json")
        with open(report_json, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        logger.info(f"Saved comparison report: {report_json}")
        
        # Create visualizations
        viz_path = self.create_visualizations()
        
        # Generate summary statistics file
        summary_path = self._save_summary_stats()
        
        return {
            'report': report,
            'files': {
                'detailed_csv': comparison_csv,
                'report_json': report_json,
                'visualizations': viz_path,
                'summary_stats': summary_path
            }
        }
    
    def _generate_summary_insights(self):
        """Generate high-level insights from the comparison"""
        insights = []
        
        # Overall agreement
        agreement_rate = self.agreement_metrics['label_agreement']
        if agreement_rate > 0.8:
            insights.append(f"High model agreement: {agreement_rate:.1%} of predictions match")
        elif agreement_rate > 0.6:
            insights.append(f"Moderate model agreement: {agreement_rate:.1%} of predictions match")
        else:
            insights.append(f"Low model agreement: {agreement_rate:.1%} of predictions match")
        
        # Best correlated sentiment
        correlations = self.agreement_metrics['score_correlations']
        best_corr = max(correlations.keys(), key=lambda x: correlations[x]['pearson'])
        insights.append(f"Strongest score correlation: {best_corr} sentiment ({correlations[best_corr]['pearson']:.3f})")
        
        # Most common disagreement
        patterns = self.disagreement_analysis['disagreement_patterns']
        if patterns:
            most_common = max(patterns.keys(), key=lambda x: patterns[x])
            insights.append(f"Most common disagreement: {most_common.replace('_to_', ' → ')} ({patterns[most_common]} cases)")
        
        # Confidence analysis
        finbert_conf = self.performance_metrics['confidence_analysis']['finbert_confidence']['mean']
        llama3_conf = self.performance_metrics['confidence_analysis']['llama3_confidence']['mean']
        if finbert_conf > llama3_conf:
            insights.append(f"FinBERT shows higher average confidence ({finbert_conf:.3f} vs {llama3_conf:.3f})")
        else:
            insights.append(f"Llama 3 shows higher average confidence ({llama3_conf:.3f} vs {finbert_conf:.3f})")
        
        return insights
    
    def _save_summary_stats(self):
        """Save summary statistics in readable format"""
        summary_path = os.path.join(self.output_dir, "comparison_summary.txt")
        
        with open(summary_path, 'w') as f:
            f.write("MODEL COMPARISON SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Posts Compared: {len(self.df_comparison)}\n\n")
            
            f.write("AGREEMENT METRICS\n")
            f.write("-" * 20 + "\n")
            f.write(f"Overall Label Agreement: {self.agreement_metrics['label_agreement']:.1%}\n")
            
            f.write("\nScore Correlations (Pearson):\n")
            for sentiment in ['positive', 'negative', 'neutral']:
                corr = self.agreement_metrics['score_correlations'][sentiment]['pearson']
                f.write(f"  {sentiment.capitalize()}: {corr:.3f}\n")
            
            f.write(f"\nTotal Disagreements: {self.disagreement_analysis['total_disagreements']} ")
            f.write(f"({self.disagreement_analysis['disagreement_rate']:.1%})\n")
            
            f.write("\nMost Common Disagreement Patterns:\n")
            patterns = sorted(self.disagreement_analysis['disagreement_patterns'].items(), 
                            key=lambda x: x[1], reverse=True)[:5]
            for pattern, count in patterns:
                f.write(f"  {pattern.replace('_to_', ' → ')}: {count} cases\n")
            
            f.write("\nCONFIDENCE ANALYSIS\n")
            f.write("-" * 20 + "\n")
            finbert_conf = self.performance_metrics['confidence_analysis']['finbert_confidence']
            llama3_conf = self.performance_metrics['confidence_analysis']['llama3_confidence']
            
            f.write(f"FinBERT Average Confidence: {finbert_conf['mean']:.3f}\n")
            f.write(f"Llama 3.1 Average Confidence: {llama3_conf['mean']:.3f}\n")
            
            f.write(f"FinBERT High Confidence (>0.8): {finbert_conf['high_confidence_count']} posts\n")
            f.write(f"Llama 3.1 High Confidence (>0.8): {llama3_conf['high_confidence_count']} posts\n")
            
            if hasattr(self, 'sector_analysis'):
                f.write("\nSECTOR ANALYSIS\n")
                f.write("-" * 20 + "\n")
                for sector, data in self.sector_analysis.items():
                    f.write(f"{sector.capitalize()}: {data['post_count']} posts, ")
                    f.write(f"{data['agreement_rate']:.1%} agreement\n")
            
            f.write("\nKEY INSIGHTS\n")
            f.write("-" * 20 + "\n")
            for insight in self._generate_summary_insights():
                f.write(f"• {insight}\n")
        
        logger.info(f"Saved summary statistics: {summary_path}")
        return summary_path

def main():
    """Main function to run model comparison"""
    logger.info("Starting Model Comparison Analysis")
    
    # File paths
    finbert_results = "data/FinBERT_data/finbert_sentiment_results.csv"
    llama3_results = "data/llama3_data/llama3_sentiment_results.csv"
    output_dir = "data/comparison"
    
    try:
        # Initialize comparison framework
        comparison = ModelComparisonFramework(finbert_results, llama3_results, output_dir)
        
        # Load results
        if not comparison.load_results():
            logger.error("Failed to load model results")
            return
        
        # Generate comprehensive comparison report
        results = comparison.generate_comparison_report()
        
        # Print summary
        logger.info("✅ Model comparison completed!")
        logger.info(f"📊 Compared {len(comparison.df_comparison)} posts")
        
        agreement_rate = results['report']['agreement_analysis']['label_agreement']
        logger.info(f"🤝 Overall Agreement: {agreement_rate:.1%}")
        
        disagreement_rate = results['report']['disagreement_analysis']['disagreement_rate']
        logger.info(f"🔄 Disagreement Rate: {disagreement_rate:.1%}")
        
        logger.info("📈 Top Insights:")
        for insight in results['report']['summary_insights']:
            logger.info(f"  • {insight}")
        
        logger.info("📁 Output files:")
        for file_type, path in results['files'].items():
            logger.info(f"  {file_type}: {path}")
        
    except Exception as e:
        logger.error(f"Error in comparison analysis: {e}")
        raise

if __name__ == "__main__":
    main() 