"""
Incremental Trump Tweet Scraper using Truthbrush
Scrapes <PERSON>'s posts from Truth Social with resumption capabilities and gap detection
"""

import os
import json
import time
import pickle
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Set
import pandas as pd
from tqdm import tqdm
import logging

try:
    from truthbrush.api import Api
except ImportError:
    print("Truthbrush not installed. Please install with: pip install truthbrush")
    raise

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/incremental_trump_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class IncrementalTrumpScraper:
    """Incremental scraper for <PERSON>'s Truth Social posts with resumption capabilities"""
    
    def __init__(self, output_dir: str = "data/raw"):
        self.output_dir = output_dir
        self.api = Api()
        self.trump_username = "realDonaldTrump"
        
        # Create directories
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        os.makedirs("data/state", exist_ok=True)
        
        # State management
        self.state_file = "data/state/scraper_state.pkl"
        self.posts_file = os.path.join(output_dir, "trump_posts_incremental.json")
        self.csv_file = os.path.join(output_dir, "trump_posts_incremental.csv")
        
        # Load existing state
        self.scraped_ids: Set[str] = set()
        self.last_scraped_date: Optional[datetime] = None
        self.total_posts_scraped = 0
        self.session_start_time = datetime.now()
        
        self._load_state()
        logger.info(f"Initialized Incremental Trump Scraper. Output directory: {output_dir}")
        logger.info(f"Already scraped {len(self.scraped_ids)} posts")
    
    def _load_state(self):
        """Load scraper state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'rb') as f:
                    state = pickle.load(f)
                    self.scraped_ids = state.get('scraped_ids', set())
                    self.last_scraped_date = state.get('last_scraped_date')
                    self.total_posts_scraped = state.get('total_posts_scraped', 0)
                    logger.info(f"Loaded state: {len(self.scraped_ids)} posts, last date: {self.last_scraped_date}")
        except Exception as e:
            logger.warning(f"Could not load state: {e}")
            self.scraped_ids = set()
            self.last_scraped_date = None
            self.total_posts_scraped = 0
    
    def _save_state(self):
        """Save scraper state to file"""
        try:
            state = {
                'scraped_ids': self.scraped_ids,
                'last_scraped_date': self.last_scraped_date,
                'total_posts_scraped': self.total_posts_scraped,
                'session_start_time': self.session_start_time
            }
            with open(self.state_file, 'wb') as f:
                pickle.dump(state, f)
            logger.info(f"Saved state: {len(self.scraped_ids)} posts")
        except Exception as e:
            logger.error(f"Failed to save state: {e}")
    
    def _load_existing_posts(self) -> List[Dict]:
        """Load existing posts from file"""
        posts = []
        try:
            if os.path.exists(self.posts_file):
                with open(self.posts_file, 'r', encoding='utf-8') as f:
                    posts = json.load(f)
                logger.info(f"Loaded {len(posts)} existing posts from file")
        except Exception as e:
            logger.warning(f"Could not load existing posts: {e}")
        return posts
    
    def _save_posts(self, posts: List[Dict]):
        """Save posts to file"""
        try:
            # Convert datetime objects to strings
            serializable_posts = []
            for post in posts:
                post_copy = post.copy()
                if post_copy.get('created_at'):
                    if isinstance(post_copy['created_at'], datetime):
                        post_copy['created_at'] = post_copy['created_at'].isoformat()
                serializable_posts.append(post_copy)
            
            with open(self.posts_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_posts, f, indent=2, ensure_ascii=False)
            
            # Also save as CSV
            df = pd.DataFrame(serializable_posts)
            if 'created_at' in df.columns:
                df['created_at'] = df['created_at'].astype(str)
                # Sort by created_at in descending order (newest first)
                df = df.sort_values('created_at', ascending=False)
            for col in ['hashtags', 'mentions']:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)
            df.to_csv(self.csv_file, index=False, encoding='utf-8')
            
            logger.info(f"Saved {len(posts)} posts to {self.posts_file} and {self.csv_file}")
        except Exception as e:
            logger.error(f"Failed to save posts: {e}")
    
    def get_user_info(self) -> Optional[Dict]:
        """Get Trump's user information"""
        try:
            user_info = self.api.lookup(self.trump_username)
            logger.info(f"Retrieved user info for {self.trump_username}")
            return user_info
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def scrape_incremental(self, months_back: int = 6, batch_size: int = 50, 
                          max_posts_per_session: Optional[int] = None) -> Dict:
        """
        Scrape posts incrementally with resumption capabilities
        
        Args:
            months_back: Number of months to go back
            batch_size: Number of posts to process before saving
            max_posts_per_session: Maximum posts to scrape in this session
        """
        logger.info(f"Starting incremental scraping for {months_back} months")
        
        # Calculate date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=months_back * 30)
        logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Load existing posts
        existing_posts = self._load_existing_posts()
        all_posts = existing_posts.copy()
        
        # Track new posts in this session
        new_posts_this_session = 0
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        try:
            # Add delay before starting
            time.sleep(3)
            
            # Get posts from API
            api_posts = self.api.pull_statuses(
                username=self.trump_username,
                replies=False,
                verbose=False,
                created_after=start_date,
                pinned=False
            )
            
            for post in tqdm(api_posts, desc="Scraping posts incrementally", unit="posts"):
                try:
                    # Check if we've reached the session limit
                    if max_posts_per_session and new_posts_this_session >= max_posts_per_session:
                        logger.info(f"Reached session limit: {max_posts_per_session}")
                        break
                    
                    post_id = str(post.get('id', ''))
                    if not post_id:
                        continue
                    
                    # Skip if already scraped
                    if post_id in self.scraped_ids:
                        continue
                    
                    # Process the post
                    processed_post = self._process_post(post)
                    if processed_post:
                        # Add to our collections (insert at beginning to maintain newest-first order)
                        all_posts.insert(0, processed_post)
                        self.scraped_ids.add(post_id)
                        new_posts_this_session += 1
                        self.total_posts_scraped += 1
                        
                        # Update last scraped date
                        post_date = self._parse_post_date(post)
                        if post_date:
                            if self.last_scraped_date is None or post_date > self.last_scraped_date:
                                self.last_scraped_date = post_date
                        
                        # Save periodically
                        if new_posts_this_session % batch_size == 0:
                            logger.info(f"Processed {new_posts_this_session} new posts this session")
                            self._save_posts(all_posts)
                            self._save_state()
                            
                            # Add delay to be respectful
                            time.sleep(2)
                        
                        # Check if we've gone too far back in time
                        if post_date and post_date < start_date:
                            logger.info(f"Reached posts older than {months_back} months")
                            break
                        
                        consecutive_errors = 0  # Reset error counter on success
                    
                    # Small delay between posts
                    time.sleep(0.2)
                    
                except Exception as e:
                    consecutive_errors += 1
                    logger.warning(f"Error processing post {post.get('id', 'unknown')}: {e}")
                    
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f"Too many consecutive errors ({consecutive_errors}), pausing...")
                        time.sleep(30)  # Longer pause on errors
                        consecutive_errors = 0
                    
                    continue
            
            # Final save
            if new_posts_this_session > 0:
                self._save_posts(all_posts)
                self._save_state()
            
            # Generate summary
            session_duration = datetime.now() - self.session_start_time
            results = {
                'success': True,
                'total_posts': len(all_posts),
                'new_posts_this_session': new_posts_this_session,
                'session_duration': str(session_duration),
                'date_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'files': {
                    'json': self.posts_file,
                    'csv': self.csv_file,
                    'state': self.state_file
                }
            }
            
            logger.info(f"Incremental scraping completed successfully!")
            logger.info(f"Total posts: {len(all_posts)}")
            logger.info(f"New posts this session: {new_posts_this_session}")
            logger.info(f"Session duration: {session_duration}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error during incremental scraping: {e}")
            # Save what we have
            if new_posts_this_session > 0:
                self._save_posts(all_posts)
                self._save_state()
            
            return {
                'success': False,
                'error': str(e),
                'total_posts': len(all_posts),
                'new_posts_this_session': new_posts_this_session
            }
    
    def _parse_post_date(self, post: Dict) -> Optional[datetime]:
        """Parse post date from various formats"""
        try:
            date_fields = ['created_at', 'date', 'timestamp', 'published_at']
            for field in date_fields:
                if field in post:
                    date_str = post[field]
                    if isinstance(date_str, str):
                        for fmt in ['%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%SZ', '%Y-%m-%d %H:%M:%S']:
                            try:
                                return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)
                            except ValueError:
                                continue
                        # Try ISO format
                        try:
                            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        except Exception:
                            pass
                    elif isinstance(date_str, (int, float)):
                        return datetime.fromtimestamp(date_str, tz=timezone.utc)
            return None
        except Exception as e:
            logger.error(f"Error parsing post date: {e}")
            return None
    
    def _process_post(self, post: Dict) -> Optional[Dict]:
        """Process a single post"""
        try:
            processed = {
                'id': post.get('id'),
                'text': post.get('content', post.get('text', '')),
                'created_at': self._parse_post_date(post),
                'likes': post.get('favourites_count', post.get('likes', 0)),
                'reposts': post.get('reblogs_count', post.get('reposts', 0)),
                'replies': post.get('replies_count', post.get('replies', 0)),
                'url': post.get('url', ''),
                'is_repost': post.get('reblog', False) is not False,
                'language': post.get('language', 'en'),
                'has_media': bool(post.get('media_attachments', post.get('media', []))),
                'media_count': len(post.get('media_attachments', post.get('media', []))),
                'hashtags': self._extract_hashtags(post.get('content', post.get('text', ''))),
                'mentions': self._extract_mentions(post.get('content', post.get('text', ''))),
                'word_count': len(post.get('content', post.get('text', '')).split()),
                'character_count': len(post.get('content', post.get('text', '')))
            }
            return processed
        except Exception as e:
            logger.error(f"Error processing post: {e}")
            return None
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """Extract hashtags from text"""
        import re
        hashtags = re.findall(r'#\w+', text)
        return hashtags
    
    def _extract_mentions(self, text: str) -> List[str]:
        """Extract mentions from text"""
        import re
        mentions = re.findall(r'@\w+', text)
        return mentions
    
    def get_status(self) -> Dict:
        """Get current scraper status"""
        return {
            'total_posts_scraped': self.total_posts_scraped,
            'unique_posts': len(self.scraped_ids),
            'last_scraped_date': self.last_scraped_date.isoformat() if self.last_scraped_date else None,
            'session_start_time': self.session_start_time.isoformat(),
            'files_exist': {
                'json': os.path.exists(self.posts_file),
                'csv': os.path.exists(self.csv_file),
                'state': os.path.exists(self.state_file)
            }
        }


def main():
    """Main function for incremental scraping"""
    scraper = IncrementalTrumpScraper()
    
    # Get current status
    status = scraper.get_status()
    print(f"Current status: {status}")
    
    # Run incremental scraping
    # This will run for hours, scraping all available posts
    results = scraper.scrape_incremental(
        months_back=6,
        batch_size=50,  # Save every 50 posts
        max_posts_per_session=None  # No limit - run until complete
    )
    
    if results['success']:
        print(f"\n✅ Incremental scraping completed!")
        print(f"📊 Total posts: {results['total_posts']}")
        print(f"🆕 New posts this session: {results['new_posts_this_session']}")
        print(f"⏱️  Session duration: {results['session_duration']}")
        print(f"📁 JSON file: {results['files']['json']}")
        print(f"📁 CSV file: {results['files']['csv']}")
    else:
        print(f"\n❌ Scraping failed: {results.get('error', 'Unknown error')}")
        print(f"📊 Posts collected before error: {results['total_posts']}")


if __name__ == "__main__":
    main() 