# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# Data files - NOW INCLUDED IN PRIVATE REPO  
# (Previously excluded for public repos due to legal/size concerns)
# data/raw/*.json
# data/raw/*.csv
# data/processed/
# data/results/
# data/market/

# Logs
logs/
*.log

# State files (contain sensitive session data)
data/state/

# Temporary files
*.tmp
*.temp
.cache/

# API keys and credentials
*.pem
*.key
secrets.json
config.json

# Large model files
*.pkl
*.pickle
*.h5
*.hdf5
*.model

# Backup files
*.bak
*.backup
