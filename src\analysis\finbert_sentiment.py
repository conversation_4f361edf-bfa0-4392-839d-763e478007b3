#!/usr/bin/env python3
"""
FinBERT Sentiment Analysis for Market-Relevant Posts
Uses the FinBERT model specifically trained on financial text
to analyze sentiment of <PERSON>'s market-relevant posts
"""

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Import transformers for FinBERT
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import torch

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/finbert_sentiment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinBERTSentimentAnalyzer:
    """
    FinBERT sentiment analyzer for financial text
    """
    
    def __init__(self, model_name="yiyanghkust/finbert-tone"):
        """
        Initialize FinBERT model
        
        Args:
            model_name: HuggingFace model identifier for FinBERT
        """
        logger.info("Initializing FinBERT Sentiment Analyzer")
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize model and tokenizer
        try:
            logger.info(f"Loading model: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
            
            # Create pipeline for easier inference
            self.sentiment_pipeline = pipeline(
                "text-classification",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                return_all_scores=True
            )
            
            logger.info("FinBERT model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading FinBERT model: {e}")
            raise
    
    def analyze_text(self, text):
        """
        Analyze sentiment of a single text
        
        Args:
            text: Input text to analyze
            
        Returns:
            dict: Sentiment analysis results with scores and labels
        """
        try:
            # Clean and prepare text
            cleaned_text = self.preprocess_text(text)
            
            # Get sentiment predictions
            results = self.sentiment_pipeline(cleaned_text)
            
            # Process results
            sentiment_scores = {}
            for result in results[0]:  # Results is a list with one element
                label = result['label'].lower()
                score = result['score']
                sentiment_scores[label] = score
            
            # Determine primary sentiment
            primary_sentiment = max(sentiment_scores.items(), key=lambda x: x[1])
            
            return {
                'text': cleaned_text,
                'sentiment_scores': sentiment_scores,
                'primary_sentiment': primary_sentiment[0],
                'primary_score': primary_sentiment[1],
                'positive_score': sentiment_scores.get('positive', 0.0),
                'negative_score': sentiment_scores.get('negative', 0.0),
                'neutral_score': sentiment_scores.get('neutral', 0.0)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing text: {e}")
            return {
                'text': text,
                'sentiment_scores': {'positive': 0.0, 'negative': 0.0, 'neutral': 1.0},
                'primary_sentiment': 'neutral',
                'primary_score': 1.0,
                'positive_score': 0.0,
                'negative_score': 0.0,
                'neutral_score': 1.0,
                'error': str(e)
            }
    
    def preprocess_text(self, text):
        """
        Clean and preprocess text for sentiment analysis
        
        Args:
            text: Raw text
            
        Returns:
            str: Cleaned text
        """
        if not isinstance(text, str):
            text = str(text)
        
        # Basic cleaning
        text = text.strip()
        
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Truncate if too long (FinBERT has token limits)
        max_length = 512  # FinBERT max sequence length
        if len(text) > max_length:
            text = text[:max_length]
            logger.warning(f"Text truncated to {max_length} characters")
        
        return text
    
    def analyze_batch(self, texts, batch_size=32):
        """
        Analyze sentiment for a batch of texts
        
        Args:
            texts: List of texts to analyze
            batch_size: Number of texts to process at once
            
        Returns:
            list: List of sentiment analysis results
        """
        results = []
        total = len(texts)
        
        logger.info(f"Processing {total} texts in batches of {batch_size}")
        
        for i in range(0, total, batch_size):
            batch = texts[i:i + batch_size]
            batch_results = []
            
            for j, text in enumerate(batch):
                logger.info(f"Processing text {i + j + 1}/{total}")
                result = self.analyze_text(text)
                batch_results.append(result)
            
            results.extend(batch_results)
            
            # Log progress
            if (i + batch_size) % (batch_size * 4) == 0 or (i + batch_size) >= total:
                logger.info(f"Completed {min(i + batch_size, total)}/{total} texts")
        
        return results

def load_market_posts(file_path):
    """
    Load market-relevant posts from CSV file
    
    Args:
        file_path: Path to CSV file
        
    Returns:
        pandas.DataFrame: Loaded posts data
    """
    try:
        logger.info(f"Loading market posts from: {file_path}")
        df = pd.read_csv(file_path)
        logger.info(f"Loaded {len(df)} market-relevant posts")
        return df
    except Exception as e:
        logger.error(f"Error loading posts: {e}")
        raise

def save_results(df_results, output_dir):
    """
    Save sentiment analysis results to multiple formats
    
    Args:
        df_results: DataFrame with sentiment results
        output_dir: Output directory path
    """
    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save detailed results as CSV
        csv_path = os.path.join(output_dir, "finbert_sentiment_results.csv")
        df_results.to_csv(csv_path, index=False)
        logger.info(f"Saved detailed results to: {csv_path}")
        
        # Save as JSON for easier programmatic access
        json_path = os.path.join(output_dir, "finbert_sentiment_results.json")
        df_results.to_json(json_path, orient='records', indent=2, date_format='iso')
        logger.info(f"Saved JSON results to: {json_path}")
        
        # Create summary statistics
        summary = create_summary_stats(df_results)
        summary_path = os.path.join(output_dir, "sentiment_summary.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        logger.info(f"Saved summary to: {summary_path}")
        
        return csv_path, json_path, summary_path
        
    except Exception as e:
        logger.error(f"Error saving results: {e}")
        raise

def create_summary_stats(df):
    """
    Create summary statistics for sentiment analysis results
    
    Args:
        df: DataFrame with sentiment results
        
    Returns:
        dict: Summary statistics
    """
    summary = {
        'analysis_timestamp': datetime.now().isoformat(),
        'total_posts': len(df),
        'sentiment_distribution': {
            'positive': int((df['primary_sentiment'] == 'positive').sum()),
            'negative': int((df['primary_sentiment'] == 'negative').sum()),
            'neutral': int((df['primary_sentiment'] == 'neutral').sum())
        },
        'average_scores': {
            'positive': float(df['positive_score'].mean()),
            'negative': float(df['negative_score'].mean()),
            'neutral': float(df['neutral_score'].mean())
        },
        'score_statistics': {
            'positive': {
                'mean': float(df['positive_score'].mean()),
                'std': float(df['positive_score'].std()),
                'min': float(df['positive_score'].min()),
                'max': float(df['positive_score'].max())
            },
            'negative': {
                'mean': float(df['negative_score'].mean()),
                'std': float(df['negative_score'].std()),
                'min': float(df['negative_score'].min()),
                'max': float(df['negative_score'].max())
            },
            'neutral': {
                'mean': float(df['neutral_score'].mean()),
                'std': float(df['neutral_score'].std()),
                'min': float(df['neutral_score'].min()),
                'max': float(df['neutral_score'].max())
            }
        }
    }
    
    # Add sector-wise sentiment if sectors available
    if 'sectors' in df.columns:
        try:
            # Parse sectors (assuming they're stored as JSON strings)
            df_copy = df.copy()
            df_copy['sectors_parsed'] = df_copy['sectors'].apply(
                lambda x: json.loads(x) if isinstance(x, str) and x.strip() else []
            )
            
            # Get all unique sectors
            all_sectors = set()
            for sectors in df_copy['sectors_parsed']:
                all_sectors.update(sectors)
            
            sector_sentiment = {}
            for sector in all_sectors:
                sector_posts = df_copy[df_copy['sectors_parsed'].apply(lambda x: sector in x)]
                if len(sector_posts) > 0:
                    sector_sentiment[sector] = {
                        'post_count': len(sector_posts),
                        'sentiment_distribution': {
                            'positive': int((sector_posts['primary_sentiment'] == 'positive').sum()),
                            'negative': int((sector_posts['primary_sentiment'] == 'negative').sum()),
                            'neutral': int((sector_posts['primary_sentiment'] == 'neutral').sum())
                        },
                        'average_positive_score': float(sector_posts['positive_score'].mean()),
                        'average_negative_score': float(sector_posts['negative_score'].mean())
                    }
            
            summary['sector_sentiment'] = sector_sentiment
            
        except Exception as e:
            logger.warning(f"Could not create sector sentiment analysis: {e}")
    
    return summary

def main():
    """
    Main function to run FinBERT sentiment analysis
    """
    logger.info("Starting FinBERT sentiment analysis")
    
    # File paths
    input_file = "data/processed/market_relevant_posts.csv"
    output_dir = "data/FinBERT_data"
    
    try:
        # Load market-relevant posts
        df_posts = load_market_posts(input_file)
        
        # Initialize FinBERT analyzer
        analyzer = FinBERTSentimentAnalyzer()
        
        # Analyze sentiment
        logger.info("Starting sentiment analysis...")
        texts = df_posts['text'].tolist()
        sentiment_results = analyzer.analyze_batch(texts, batch_size=16)
        
        # Prepare results DataFrame
        logger.info("Preparing results...")
        
        # Create new DataFrame with original data + sentiment
        df_results = df_posts.copy()
        
        # Add sentiment columns
        df_results['primary_sentiment'] = [r['primary_sentiment'] for r in sentiment_results]
        df_results['primary_score'] = [r['primary_score'] for r in sentiment_results]
        df_results['positive_score'] = [r['positive_score'] for r in sentiment_results]
        df_results['negative_score'] = [r['negative_score'] for r in sentiment_results]
        df_results['neutral_score'] = [r['neutral_score'] for r in sentiment_results]
        
        # Add processed text
        df_results['processed_text'] = [r['text'] for r in sentiment_results]
        
        # Save results
        csv_path, json_path, summary_path = save_results(df_results, output_dir)
        
        # Print summary
        logger.info("✅ FinBERT sentiment analysis completed!")
        logger.info(f"📊 Processed {len(df_results)} posts")
        
        # Print sentiment distribution
        sentiment_dist = df_results['primary_sentiment'].value_counts()
        logger.info("📈 Sentiment Distribution:")
        for sentiment, count in sentiment_dist.items():
            percentage = (count / len(df_results)) * 100
            logger.info(f"  {sentiment.capitalize()}: {count} posts ({percentage:.1f}%)")
        
        # Print average scores
        logger.info("📊 Average Sentiment Scores:")
        logger.info(f"  Positive: {df_results['positive_score'].mean():.3f}")
        logger.info(f"  Negative: {df_results['negative_score'].mean():.3f}")
        logger.info(f"  Neutral: {df_results['neutral_score'].mean():.3f}")
        
        logger.info(f"📁 Results saved to:")
        logger.info(f"  CSV: {csv_path}")
        logger.info(f"  JSON: {json_path}")
        logger.info(f"  Summary: {summary_path}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main() 