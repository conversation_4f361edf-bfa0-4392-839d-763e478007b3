# Trump Market Sentiment Analysis

This project scrapes <PERSON>'s posts from Truth Social using the Truthbrush library and prepares the data for sentiment analysis to understand market sentiment correlation.

## 🔐 Private Repository - Data Included

**✅ This is now a private repository** - All data files are included in Git!

📖 **Sentiment Comparison**: [README_sentiment_comparison.md](README_sentiment_comparison.md) - FinBERT vs Llama 3.1 comparison framework  
🔍 **Data Info**: [DATA_MANAGEMENT_GUIDE.md](DATA_MANAGEMENT_GUIDE.md) - Technical details about data management  
🚀 **Quick Setup**: Clone the repo and install dependencies - all data is included!

### Quick Start:
```bash
# Clone repository (includes all data)
git clone your-private-repo-url
cd trump-market-sentiment-analysis

# Install dependencies
pip install -r requirements.txt

# Test sentiment comparison framework
python test_comparison_framework.py

# Run FinBERT vs Llama 3.1 comparison
python run_sentiment_comparison.py --subset 10
```

## 📊 Data Files Included

**✅ All data is now in the Git repository** (private repo removes legal concerns)

📁 **Available Data:**
- `data/raw/` - Original Truth Social posts (CSV/JSON)
- `data/processed/` - Market-relevant posts with metadata  
- `data/FinBERT_data/` - FinBERT sentiment analysis results
- Additional analysis results as you generate them

No cloud sync needed - just clone and go! 🚀


### Command Line Options

#### Incremental Scraper (`run_incremental_scraper.py`)
- `--months`: Number of months to go back (default: 6)
- `--batch-size`: Posts to process before saving (default: 50)
- `--max-posts`: Maximum posts per session (default: no limit)
- `--status`: Show current scraper status
- `--reset`: Reset scraper state and start fresh

#### Basic Scraper (`run_scraper.py`)
- `--months`: Number of months to go back (default: 6)
- `--max-posts`: Maximum number of posts to scrape
- `--output-dir`: Output directory (default: data/raw)
- `--no-json`: Skip JSON output
- `--no-csv`: Skip CSV output

## Data Format

### JSON Output
```json
{
  "id": "",
  "text": "",
  "created_at": "",
  "likes": ,
  "reposts": ,
  "replies": ,
  "url": "",
  "is_repost": false,
  "language": "",
  "has_media": false,
  "media_count": ,
  "hashtags": ["#", "#"],
  "mentions": ["@"],
  "word_count": ,
  "character_count": 
}
```

### CSV Output
The CSV file contains the same data in tabular format with columns for each field.


## Security & Legal Notes

- **Credentials**: Never commit `.env` files or credentials to Git  
- **Private Repository**: Data files now included in private repo (legal concerns resolved)
- **State Files**: Scraper state files contain session tokens and remain excluded
- **ToS Compliance**: Data collection respects platform terms of service and rate limits
- **Research Use**: Data is collected for academic/research purposes under fair use

## License

This project is for educational and research purposes. Please respect Truth Social's terms of service and rate limits.

## Troubleshooting

### Common Issues

1. **Rate Limiting**: If you encounter rate limits, wait and try again later
2. **Authentication**: Ensure your Truth Social credentials are correct
3. **Dependencies**: Make sure all requirements are installed
4. **Resume Scraping**: Use the incremental scraper to resume interrupted sessions

### Logs
Check the `logs/` directory for detailed error information.

## Data Privacy

This project collects publicly available data from Truth Social. All data collection follows the platform's terms of service and respects user privacy.
