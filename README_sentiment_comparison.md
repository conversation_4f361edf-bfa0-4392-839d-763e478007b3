# Sentiment Analysis Comparison Framework

This framework provides comprehensive comparison between FinBERT and Llama 3.1 for sentiment analysis on <PERSON>'s market-relevant posts. It includes automated analysis pipelines, detailed comparison metrics, and visualizations.

## Overview

The framework analyzes **371 market-relevant posts** using two state-of-the-art sentiment analysis approaches:

- **FinBERT**: Financial domain-specific BERT model (`yiyanghkust/finbert-tone`)
- **Llama 3.1**: General-purpose instruction-following model (`meta-llama/Meta-Llama-3.1-8B-Instruct`) with prompt-based classification

## 🔐 Authentication Required

**⚠️ Important**: Llama 3.1 models are gated on Hugging Face. You need approval before using them.

### Quick Setup:
1. **Get Access**: Visit [meta-llama/Meta-Llama-3.1-8B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-8B-Instruct) and request access
2. **Create Token**: Go to [HF Settings](https://huggingface.co/settings/tokens) and create a Read token  
3. **Login**: Run `huggingface-cli login` and paste your token

📖 **Detailed Guide**: See [LLAMA31_ACCESS_GUIDE.md](LLAMA31_ACCESS_GUIDE.md) for complete instructions.

## Features

### 🔍 Sentiment Analysis
- **FinBERT**: Uses specialized financial text understanding with direct classification
- **Llama 3.1**: Employs structured prompts for consistent 3-class sentiment classification
- **Consistent Output**: Both models produce positive/negative/neutral labels with confidence scores

### 📊 Comparison Metrics
- **Label Agreement**: Percentage of posts where both models agree on sentiment
- **Score Correlations**: Pearson and Spearman correlations for sentiment scores
- **Disagreement Analysis**: Detailed examination of cases where models disagree
- **Confidence Analysis**: Distribution and reliability of model confidence scores

### 🏭 Sector-Wise Analysis
- **Performance by Sector**: Agreement rates across different market sectors
- **Post Type Analysis**: Comparison across various post types (company mentions, policy, etc.)
- **Market Relevance**: How agreement varies with market relevance scores

### 📈 Visualizations
- Confusion matrices showing agreement patterns
- Score correlation scatter plots
- Sentiment distribution comparisons
- Disagreement pattern analysis
- Sector-wise performance charts

## Quick Start

### Prerequisites

```bash
# Install dependencies
pip install -r requirements.txt

# Authenticate with Hugging Face (REQUIRED for Llama 3.1)
huggingface-cli login
# Paste your token when prompted

# Ensure you have GPU support for faster processing (optional)
# CUDA-compatible PyTorch installation recommended for Llama 3.1
```

### Basic Usage

```bash
# Test setup first
python test_comparison_framework.py

# Run complete comparison pipeline (371 posts)
python run_sentiment_comparison.py

# Test with subset of posts
python run_sentiment_comparison.py --subset 50

# Run only comparison (if Llama 3.1 results already exist)
python run_sentiment_comparison.py --comparison-only

# Skip Llama 3.1 analysis (use existing results)
python run_sentiment_comparison.py --skip-llama3
```

### Expected Processing Time

| Configuration | Posts | Est. Time | GPU Memory |
|--------------|-------|-----------|------------|
| Subset (50 posts) | 50 | ~5-10 min | 4-6 GB |
| Full dataset | 371 | ~30-60 min | 6-8 GB |
| CPU-only | 371 | ~2-4 hours | N/A |

## Output Files

### Data Files
- `data/llama3_data/llama3_sentiment_results.csv` - Llama 3.1 sentiment analysis results
- `data/comparison/detailed_comparison_results.csv` - Side-by-side model comparison
- `data/comparison/comparison_report.json` - Comprehensive analysis metrics

### Analysis Reports
- `data/comparison/comparison_summary.txt` - Human-readable summary
- `data/comparison/model_comparison_visualizations.png` - Complete visualization suite
- `data/comparison/confusion_matrix.png` - Agreement confusion matrix
- `data/comparison/score_correlations.png` - Score correlation analysis

### Log Files
- `logs/llama3_sentiment.log` - Llama 3.1 analysis logs
- `logs/model_comparison.log` - Comparison framework logs
- `logs/sentiment_comparison.log` - Pipeline execution logs

## Analysis Components

### 1. Llama3SentimentAnalyzer (`src/analysis/llama3_sentiment.py`)

```python
from src.analysis.llama3_sentiment import Llama3SentimentAnalyzer

# Initialize analyzer
analyzer = Llama3SentimentAnalyzer()

# Analyze single text
result = analyzer.analyze_text("Market volatility continues...")

# Batch analysis
results = analyzer.analyze_batch(texts)
```

**Key Features:**
- Structured prompt engineering for consistent responses
- Deterministic generation (temperature=0.1) for reproducibility
- Automatic response parsing with fallback handling
- Processing time tracking for performance analysis

### 2. ModelComparisonFramework (`src/analysis/model_comparison.py`)

```python
from src.analysis.model_comparison import ModelComparisonFramework

# Initialize comparison
comparison = ModelComparisonFramework(
    finbert_results_path="data/FinBERT_data/finbert_sentiment_results.csv",
    llama3_results_path="data/llama3_data/llama3_sentiment_results.csv"
)

# Load and align results
comparison.load_results()

# Generate comprehensive report
results = comparison.generate_comparison_report()
```

**Analysis Methods:**
- `calculate_agreement_metrics()` - Label agreement and score correlations
- `analyze_disagreements()` - Detailed disagreement pattern analysis
- `analyze_sector_performance()` - Sector-wise comparison
- `create_visualizations()` - Generate comparison charts

## Comparison Metrics Explained

### Agreement Metrics
- **Label Agreement**: `(FinBERT_label == Llama3_label).mean()`
- **Pearson Correlation**: Linear relationship between continuous scores
- **Spearman Correlation**: Rank-based correlation (non-parametric)

### Disagreement Analysis
- **Disagreement Patterns**: e.g., "positive → negative", "neutral → positive"
- **High-Confidence Disagreements**: Cases where both models are confident but disagree
- **Sector-Specific Disagreements**: How disagreements vary by market sector

### Performance Metrics
- **Processing Speed**: Time per post for each model
- **Confidence Distribution**: How confident each model is in its predictions
- **Resource Usage**: Memory and computational requirements

## Example Results

### Sample Agreement Analysis
```
Overall Label Agreement: 73.2%
Score Correlations (Pearson):
  Positive: 0.651
  Negative: 0.598
  Neutral: 0.542

Top Disagreement Patterns:
  neutral → positive: 31 cases
  positive → neutral: 28 cases
  negative → neutral: 15 cases
```

### Sample Sector Performance
```
Sector Agreement Rates:
  Financial: 78.4% (142 posts)
  Technology: 71.2% (89 posts)
  Energy: 69.8% (67 posts)
  Industrial: 72.1% (134 posts)
```

## Advanced Usage

### Custom Model Configuration

```python
# Use different Llama 3.1 variant
analyzer = Llama3SentimentAnalyzer(
    model_name="meta-llama/Meta-Llama-3.1-70B-Instruct"
)

# Custom comparison with different thresholds
comparison = ModelComparisonFramework(
    finbert_results_path="...",
    llama3_results_path="...",
    output_dir="custom_comparison"
)
```

### Analyzing Specific Subsets

```python
# Filter by sector
tech_posts = df[df['impacted_sectors'].str.contains('technology')]

# Filter by market relevance
high_relevance = df[df['market_relevance_score'] > 70]

# Filter by post type
company_mentions = df[df['post_type'].str.contains('company_mention')]
```

## Troubleshooting

### Common Issues

1. **Access Denied / Authentication Errors**
   ```bash
   # Make sure you're logged in to Hugging Face
   huggingface-cli login
   
   # Check if you have access to the model
   huggingface-cli repo info meta-llama/Meta-Llama-3.1-8B-Instruct
   ```

2. **CUDA Out of Memory**
   ```bash
   # Use smaller model or CPU-only mode
   export CUDA_VISIBLE_DEVICES=""
   python run_sentiment_comparison.py --subset 50
   ```

3. **Slow Processing**
   ```bash
   # Start with subset for testing
   python run_sentiment_comparison.py --subset 10
   ```

4. **Missing Dependencies**
   ```bash
   # Install specific versions
   pip install torch==2.0.0 transformers==4.35.0 huggingface_hub
   ```

### Error Recovery

- **Llama 3.1 Analysis Failed**: Use `--skip-llama3` to run comparison only
- **Comparison Failed**: Check if both result files exist and have matching IDs
- **Visualization Errors**: Install required packages: `pip install matplotlib seaborn`
- **Authentication Issues**: See [LLAMA31_ACCESS_GUIDE.md](LLAMA31_ACCESS_GUIDE.md)

## Performance Optimization

### For Faster Processing
1. **Use GPU**: Ensure CUDA is available and properly configured
2. **Batch Processing**: Framework automatically optimizes batch sizes
3. **Subset Testing**: Use `--subset N` for development and testing
4. **Model Selection**: 8B model for speed, 70B for accuracy

### Memory Management
- **Model Loading**: Models are loaded on-demand to conserve memory
- **Data Processing**: Large datasets are processed in chunks
- **Cleanup**: Temporary files and models are properly cleaned up

## Model Variants

### Available Llama 3.1 Models

| Model | Parameters | Memory (16-bit) | Use Case |
|-------|------------|-----------------|----------|
| `Meta-Llama-3.1-8B-Instruct` | 8B | ~16 GB | **Recommended** - Good balance |
| `Meta-Llama-3.1-70B-Instruct` | 70B | ~140 GB | Higher accuracy, requires large GPU |
| `Meta-Llama-3.1-405B-Instruct` | 405B | ~810 GB | Research use, multi-GPU setup |

## Integration with Existing Pipeline

The comparison framework integrates seamlessly with your existing analysis:

```python
# Use with existing market filter
from src.preprocessing.filter_market_posts import MarketPostFilter

# Use with existing FinBERT analysis
from src.analysis.finbert_sentiment import FinBERTSentimentAnalyzer

# Combine with comparison framework
from src.analysis.model_comparison import ModelComparisonFramework
```

## Future Enhancements

Potential areas for expansion:
- **Additional Models**: Compare with other sentiment models (VADER, TextBlob, etc.)
- **Fine-tuning**: Domain-specific fine-tuning of models
- **Real-time Analysis**: Stream processing for live sentiment comparison
- **Ensemble Methods**: Combine predictions from multiple models
- **Confidence Calibration**: Improve reliability of confidence scores

## Contributing

To extend the framework:
1. Add new model analyzers following the `FinBERTSentimentAnalyzer` pattern
2. Extend comparison metrics in `ModelComparisonFramework`
3. Add new visualization types to the framework
4. Implement additional analysis dimensions (temporal, linguistic, etc.)

## Citation

If you use this framework in research, please cite:
```
Trump Market Sentiment Analysis Comparison Framework
FinBERT vs Llama 3.1 Sentiment Analysis on Market-Relevant Posts
2025
```

