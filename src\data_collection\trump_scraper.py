"""
Trump Tweet Scraper using Truthbrush
Scrapes <PERSON>'s posts from Truth Social for sentiment analysis
"""

import os
import json
import time
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import pandas as pd
from tqdm import tqdm
import logging

try:
    from truthbrush.api import Api
except ImportError:
    print("Truthbrush not installed. Please install with: pip install truthbrush")
    raise

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trump_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TrumpTweetScraper:
    """Scraper for <PERSON>'s Truth Social posts using Truthbrush"""
    
    def __init__(self, output_dir: str = "data/raw"):
        """
        Initialize the scraper
        
        Args:
            output_dir: Directory to save scraped data
        """
        self.output_dir = output_dir
        self.api = Api()
        self.trump_username = "realDonaldTrump"  # <PERSON>'s Truth Social handle
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        logger.info(f"Initialized Trump Tweet Scraper. Output directory: {output_dir}")
    
    def get_user_info(self) -> Optional[Dict]:
        """Get Trump's user information from Truth Social"""
        try:
            user_info = self.api.lookup(self.trump_username)
            logger.info(f"Retrieved user info for {self.trump_username}")
            return user_info
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def scrape_posts(self, months_back: int = 6, max_posts: Optional[int] = None) -> List[Dict]:
        """
        Scrape Trump's posts from Truth Social
        
        Args:
            months_back: Number of months to go back
            max_posts: Maximum number of posts to scrape (None for all)
            
        Returns:
            List of post dictionaries
        """
        logger.info(f"Starting to scrape posts from {months_back} months ago")
        
        # Calculate date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=months_back * 30)
        
        logger.info(f"Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        posts = []
        try:
            # Add a small delay before starting to be more conservative
            time.sleep(2)
            
            all_posts = self.api.pull_statuses(
                username=self.trump_username,
                replies=False,
                verbose=False,
                created_after=start_date,
                pinned=False
            )
            
            count = 0
            for post in tqdm(all_posts, desc="Scraping posts", unit="posts"):
                try:
                    post_date = self._parse_post_date(post)
                    
                    if post_date and post_date >= start_date:
                        processed_post = self._process_post(post)
                        if processed_post:
                            posts.append(processed_post)
                            count += 1
                            
                            # Save partial results every 100 posts
                            if count % 100 == 0:
                                logger.info(f"Saved {count} posts so far...")
                                self._save_partial_results(posts, count)
                            
                            if max_posts and count >= max_posts:
                                logger.info(f"Reached maximum posts limit: {max_posts}")
                                break
                    elif post_date and post_date < start_date:
                        logger.info(f"Reached posts older than {months_back} months")
                        break
                        
                    # Add small delay between posts to be more conservative
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"Error processing post {post.get('id', 'unknown')}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            # Save what we have so far
            if posts:
                logger.info(f"Saving {len(posts)} posts collected before error")
                self._save_partial_results(posts, len(posts))
            raise
        
        logger.info(f"Successfully scraped {len(posts)} posts")
        return posts
    
    def _save_partial_results(self, posts: List[Dict], count: int) -> None:
        """Save partial results in case of interruption"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            partial_filename = f"trump_posts_partial_{count}_{timestamp}.json"
            filepath = os.path.join(self.output_dir, partial_filename)
            
            serializable_posts = []
            for post in posts:
                post_copy = post.copy()
                if post_copy.get('created_at'):
                    post_copy['created_at'] = post_copy['created_at'].isoformat()
                serializable_posts.append(post_copy)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_posts, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved partial results to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save partial results: {e}")
    
    def _parse_post_date(self, post: Dict) -> Optional[datetime]:
        """Parse the post date from various possible formats"""
        try:
            # Try different date fields that might exist
            date_fields = ['created_at', 'date', 'timestamp', 'published_at']
            
            for field in date_fields:
                if field in post:
                    date_str = post[field]
                    if isinstance(date_str, str):
                        # Try common date formats
                        for fmt in ['%Y-%m-%dT%H:%M:%S.%fZ', '%Y-%m-%dT%H:%M:%SZ', '%Y-%m-%d %H:%M:%S']:
                            try:
                                return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)
                            except ValueError:
                                continue
                        # Try ISO format
                        try:
                            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        except Exception:
                            pass
                    elif isinstance(date_str, (int, float)):
                        # Unix timestamp
                        return datetime.fromtimestamp(date_str, tz=timezone.utc)
            
            logger.warning(f"Could not parse date for post: {post.get('id', 'unknown')}")
            return None
            
        except Exception as e:
            logger.error(f"Error parsing post date: {e}")
            return None
    
    def _process_post(self, post: Dict) -> Optional[Dict]:
        """Process and clean a single post"""
        try:
            processed = {
                'id': post.get('id'),
                'text': post.get('content', post.get('text', '')),
                'created_at': self._parse_post_date(post),
                'likes': post.get('favourites_count', post.get('likes', 0)),
                'reposts': post.get('reblogs_count', post.get('reposts', 0)),
                'replies': post.get('replies_count', post.get('replies', 0)),
                'url': post.get('url', ''),
                'is_repost': post.get('reblog', False) is not False,
                'language': post.get('language', 'en'),
                'has_media': bool(post.get('media_attachments', post.get('media', []))),
                'media_count': len(post.get('media_attachments', post.get('media', []))),
                'hashtags': self._extract_hashtags(post.get('content', post.get('text', ''))),
                'mentions': self._extract_mentions(post.get('content', post.get('text', ''))),
                'word_count': len(post.get('content', post.get('text', '')).split()),
                'character_count': len(post.get('content', post.get('text', '')))
            }
            
            return processed
            
        except Exception as e:
            logger.error(f"Error processing post: {e}")
            return None
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """Extract hashtags from text"""
        import re
        hashtags = re.findall(r'#\w+', text)
        return hashtags
    
    def _extract_mentions(self, text: str) -> List[str]:
        """Extract mentions from text"""
        import re
        mentions = re.findall(r'@\w+', text)
        return mentions
    
    def save_to_json(self, posts: List[Dict], filename: Optional[str] = None) -> str:
        """Save posts to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trump_posts_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # Convert datetime objects to strings for JSON serialization
        serializable_posts = []
        for post in posts:
            post_copy = post.copy()
            if post_copy.get('created_at'):
                post_copy['created_at'] = post_copy['created_at'].isoformat()
            serializable_posts.append(post_copy)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_posts, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {len(posts)} posts to {filepath}")
        return filepath
    
    def save_to_csv(self, posts: List[Dict], filename: Optional[str] = None) -> str:
        """Save posts to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"trump_posts_{timestamp}.csv"
        
        filepath = os.path.join(self.output_dir, filename)
        
        # Convert to DataFrame
        df = pd.DataFrame(posts)
        
        # Convert datetime objects to strings for CSV
        if 'created_at' in df.columns:
            df['created_at'] = df['created_at'].astype(str)
        
        # Convert lists to strings
        for col in ['hashtags', 'mentions']:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)
        
        df.to_csv(filepath, index=False, encoding='utf-8')
        logger.info(f"Saved {len(posts)} posts to {filepath}")
        return filepath
    
    def run_scraping(self, months_back: int = 6, max_posts: Optional[int] = None, 
                    save_json: bool = True, save_csv: bool = True) -> Dict:
        """
        Run the complete scraping process
        
        Args:
            months_back: Number of months to go back
            max_posts: Maximum number of posts to scrape
            save_json: Whether to save as JSON
            save_csv: Whether to save as CSV
            
        Returns:
            Dictionary with scraping results and file paths
        """
        logger.info("Starting Trump tweet scraping process")
        
        # Get user info first
        user_info = self.get_user_info()
        
        # Scrape posts
        posts = self.scrape_posts(months_back=months_back, max_posts=max_posts)
        
        if not posts:
            logger.warning("No posts were scraped")
            return {
                'success': False,
                'posts_count': 0,
                'error': 'No posts found'
            }
        
        # Save data
        results = {
            'success': True,
            'posts_count': len(posts),
            'user_info': user_info,
            'date_range': {
                'start': min(post['created_at'] for post in posts if post['created_at']),
                'end': max(post['created_at'] for post in posts if post['created_at'])
            }
        }
        
        if save_json:
            results['json_file'] = self.save_to_json(posts)
        
        if save_csv:
            results['csv_file'] = self.save_to_csv(posts)
        
        logger.info(f"Scraping completed successfully. Scraped {len(posts)} posts.")
        return results


def main():
    """Main function to run the scraper"""
    scraper = TrumpTweetScraper()
    
    # Run scraping for 6 months
    results = scraper.run_scraping(
        months_back=6,
        max_posts=None,  # No limit
        save_json=True,
        save_csv=True
    )
    
    print(f"Scraping completed!")
    print(f"Posts scraped: {results['posts_count']}")
    if 'json_file' in results:
        print(f"JSON file: {results['json_file']}")
    if 'csv_file' in results:
        print(f"CSV file: {results['csv_file']}")


if __name__ == "__main__":
    main() 