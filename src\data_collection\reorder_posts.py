#!/usr/bin/env python3
"""
Script to reorder existing posts so newest posts appear at the top and regenerate JSON from CSV
"""

import pandas as pd
import json
import os

def reorder_posts():
    """Reorder posts so newest appear first and regenerate JSON from CSV"""
    
    # File paths
    csv_file = "data/raw/trump_posts_incremental.csv"
    json_file = "data/raw/trump_posts_incremental.json"
    
    print("Reordering posts to put newest first...")
    
    # Read CSV file
    if os.path.exists(csv_file):
        print(f"Reading CSV file: {csv_file}")
        df = pd.read_csv(csv_file)
        
        # Convert created_at to datetime
        df['created_at'] = pd.to_datetime(df['created_at'], format='mixed')
        
        # Sort by created_at in descending order (newest first)
        df_sorted = df.sort_values('created_at', ascending=False)
        
        # Save reordered CSV
        df_sorted.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"Saved reordered CSV with {len(df_sorted)} posts")
        
        # Regenerate JSON file from sorted CSV
        print(f"Regenerating JSON file from sorted CSV: {json_file}")
        # Convert datetimes to string for JSON
        df_sorted['created_at'] = df_sorted['created_at'].astype(str)
        posts = df_sorted.to_dict('records')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(posts, f, indent=2, ensure_ascii=False)
        print(f"Saved regenerated JSON with {len(posts)} posts")
    else:
        print("CSV file not found. Nothing to reorder.")
    
    print("✅ Posts reordered and JSON regenerated successfully!")
    
    # Show the first few posts to verify order
    if os.path.exists(csv_file):
        print("\nFirst 5 posts (newest first):")
        print("=" * 80)
        for i, row in df_sorted.head(5).iterrows():
            print(f"Date: {row['created_at']}")
            print(f"Text: {row['text'][:200]}{'...' if len(row['text']) > 200 else ''}")
            print(f"Likes: {row['likes']}, Reposts: {row['reposts']}, Replies: {row['replies']}")
            print("-" * 80)

if __name__ == "__main__":
    reorder_posts() 