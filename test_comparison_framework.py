#!/usr/bin/env python3
"""
Test Script for Sentiment Analysis Comparison Framework
Tests the framework with a small subset of data to ensure everything works correctly.

Usage:
    python test_comparison_framework.py
"""

import sys
import os
import logging
import traceback
import pandas as pd

# Add src to path for imports
sys.path.append('src')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_availability():
    """Test if required data files are available"""
    logger.info("Testing data availability...")
    
    required_files = [
        "data/processed/market_relevant_posts.csv",
        "data/FinBERT_data/finbert_sentiment_results.csv"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            logger.info(f"✅ {file_path}: {len(df)} rows")
        else:
            logger.error(f"❌ Missing: {file_path}")
            return False
    
    return True

def test_llama3_analyzer():
    """Test Llama 3.1 sentiment analyzer with sample text"""
    logger.info("Testing Llama 3.1 analyzer...")
    
    try:
        from analysis.llama3_sentiment import Llama3SentimentAnalyzer
        
        # Test sample texts
        test_texts = [
            "The stock market is doing great! Record highs everywhere!",
            "Market volatility is concerning investors today.",
            "The Fed announced new interest rate policies."
        ]
        
        # Note: This would require model download, so we'll just test import and structure
        logger.info("✅ Llama 3.1 analyzer import successful")
        logger.info(f"✅ Test texts prepared: {len(test_texts)} samples")
        
        # We'll skip actual model loading in test to avoid long download times
        logger.info("⏩ Skipping actual model loading in test mode")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Llama 3.1 analyzer test failed: {e}")
        return False

def test_comparison_framework():
    """Test the comparison framework structure"""
    logger.info("Testing comparison framework...")
    
    try:
        from analysis.model_comparison import ModelComparisonFramework
        
        # Test initialization (without actual file loading)
        logger.info("✅ Model comparison framework import successful")
        
        # Test file paths
        finbert_path = "data/FinBERT_data/finbert_sentiment_results.csv"
        llama3_path = "data/llama3_data/llama3_sentiment_results.csv"
        
        if os.path.exists(finbert_path):
            logger.info("✅ FinBERT results file exists")
        else:
            logger.warning("⚠️  FinBERT results file not found (expected for first run)")
        
        if os.path.exists(llama3_path):
            logger.info("✅ Llama 3.1 results file exists")
        else:
            logger.warning("⚠️  Llama 3.1 results file not found (expected for first run)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Comparison framework test failed: {e}")
        return False

def test_pipeline_script():
    """Test the main pipeline script structure"""
    logger.info("Testing pipeline script...")
    
    try:
        # Check if the script exists and is readable
        script_path = "run_sentiment_comparison.py"
        if os.path.exists(script_path):
            logger.info("✅ Pipeline script exists")
            
            # Read and basic syntax check
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'def main()' in content and 'argparse' in content:
                    logger.info("✅ Pipeline script structure looks correct")
                else:
                    logger.warning("⚠️  Pipeline script missing expected structure")
        else:
            logger.error("❌ Pipeline script not found")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Pipeline script test failed: {e}")
        return False

def test_sample_data_processing():
    """Test processing a small sample of actual data"""
    logger.info("Testing sample data processing...")
    
    try:
        # Load a small sample of market posts
        posts_path = "data/processed/market_relevant_posts.csv"
        if not os.path.exists(posts_path):
            logger.warning("⚠️  Market posts file not found, skipping sample processing test")
            return True
        
        df = pd.read_csv(posts_path)
        sample_df = df.head(3)  # Use just 3 posts for testing
        
        logger.info(f"✅ Loaded sample data: {len(sample_df)} posts")
        
        # Check required columns
        required_cols = ['id', 'text', 'impacted_sectors', 'post_type', 'market_relevance_score']
        missing_cols = [col for col in required_cols if col not in sample_df.columns]
        
        if missing_cols:
            logger.warning(f"⚠️  Missing columns: {missing_cols}")
        else:
            logger.info("✅ All required columns present")
        
        # Test basic text processing
        sample_texts = sample_df['text'].tolist()
        for i, text in enumerate(sample_texts):
            cleaned_text = str(text).strip()[:100] + "..." if len(str(text)) > 100 else str(text)
            logger.info(f"✅ Sample {i+1}: {cleaned_text}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Sample data processing test failed: {e}")
        return False

def test_dependencies():
    """Test if all required dependencies are available"""
    logger.info("Testing dependencies...")
    
    required_packages = [
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('transformers', 'transformers'),
        ('torch', 'torch'),
        ('sklearn', 'scikit-learn'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('scipy', 'scipy')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
            logger.info(f"✅ {package_name}")
        except ImportError:
            logger.error(f"❌ {package_name} not installed")
            missing_packages.append(package_name)
    
    if missing_packages:
        logger.error("Missing packages. Install with:")
        logger.error(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("SENTIMENT COMPARISON FRAMEWORK TEST SUITE")
    logger.info("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Data Availability", test_data_availability),
        ("Sample Data Processing", test_sample_data_processing),
        ("Llama 3.1 Analyzer", test_llama3_analyzer),
        ("Comparison Framework", test_comparison_framework),
        ("Pipeline Script", test_pipeline_script)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            logger.error(traceback.format_exc())
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Framework is ready to use.")
        logger.info("\nNext steps:")
        logger.info("1. Run with subset: python run_sentiment_comparison.py --subset 10")
        logger.info("2. Review output files in data/llama3_data/ and data/comparison/")
        logger.info("3. Scale up to full dataset when ready")
    else:
        logger.info("⚠️  Some tests failed. Please address issues before running full pipeline.")
        
        if not results.get("Dependencies", True):
            logger.info("Install missing dependencies: pip install -r requirements.txt")
        
        if not results.get("Data Availability", True):
            logger.info("Ensure data files are present and properly formatted")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 