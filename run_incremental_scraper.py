#!/usr/bin/env python3
"""
Incremental Trump Tweet Scraper
Usage: python run_incremental_scraper.py [--months 6] [--batch-size 50] [--max-posts 1000] [--status]
"""

import argparse
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_collection.incremental_trump_scraper import IncrementalTrumpScraper


def main():
    parser = argparse.ArgumentParser(description='Incremental Trump tweet scraper with resumption capabilities')
    parser.add_argument('--months', type=int, default=6, 
                       help='Number of months to go back (default: 6)')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Number of posts to process before saving (default: 50)')
    parser.add_argument('--max-posts', type=int, default=None,
                       help='Maximum posts to scrape in this session (default: no limit)')
    parser.add_argument('--status', action='store_true',
                       help='Show current scraper status and exit')
    parser.add_argument('--reset', action='store_true',
                       help='Reset scraper state and start fresh')
    
    args = parser.parse_args()
    
    if args.reset:
        print("🗑️  Resetting scraper state...")
        state_file = "data/state/scraper_state.pkl"
        if os.path.exists(state_file):
            os.remove(state_file)
            print("✅ State file removed")
        else:
            print("ℹ️  No state file found")
    
    scraper = IncrementalTrumpScraper()
    
    # Show status if requested
    if args.status:
        status = scraper.get_status()
        print(f"\n📊 Current Scraper Status:")
        print(f"   Total posts scraped: {status['total_posts_scraped']}")
        print(f"   Unique posts: {status['unique_posts']}")
        print(f"   Last scraped date: {status['last_scraped_date']}")
        print(f"   Session start time: {status['session_start_time']}")
        print(f"   Files exist:")
        for file_type, exists in status['files_exist'].items():
            print(f"     {file_type}: {'✅' if exists else '❌'}")
        return
    
    print(f"🚀 Starting incremental Trump tweet scraper...")
    print(f"📅 Months back: {args.months}")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"📊 Max posts per session: {args.max_posts or 'No limit'}")
    
    # Show current status
    status = scraper.get_status()
    print(f"📈 Current progress: {status['unique_posts']} posts already scraped")
    
    try:
        results = scraper.scrape_incremental(
            months_back=args.months,
            batch_size=args.batch_size,
            max_posts_per_session=args.max_posts
        )
        
        if results['success']:
            print(f"\n✅ Incremental scraping completed successfully!")
            print(f"📊 Total posts: {results['total_posts']}")
            print(f"🆕 New posts this session: {results['new_posts_this_session']}")
            print(f"⏱️  Session duration: {results['session_duration']}")
            print(f"📁 JSON file: {results['files']['json']}")
            print(f"📁 CSV file: {results['files']['csv']}")
            print(f"💾 State file: {results['files']['state']}")
        else:
            print(f"\n❌ Scraping failed: {results.get('error', 'Unknown error')}")
            print(f"📊 Posts collected before error: {results['total_posts']}")
            print(f"🆕 New posts this session: {results['new_posts_this_session']}")
            print(f"\n💡 You can resume later by running the same command again!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️  Scraping interrupted by user")
        print(f"💾 Progress has been saved. You can resume later!")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during scraping: {e}")
        print(f"💾 Progress has been saved. You can resume later!")
        sys.exit(1)


if __name__ == "__main__":
    main() 