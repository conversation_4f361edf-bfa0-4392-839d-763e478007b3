#!/usr/bin/env python3
"""
Data Synchronization Script for Sentiment Analysis Project
Helps sync data files between different computers using cloud storage.

Usage:
    python sync_data.py --backup     # Backup data to cloud storage
    python sync_data.py --restore    # Restore data from cloud storage
    python sync_data.py --status     # Check what data exists
"""

import os
import shutil
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataSyncer:
    """Manages data synchronization for the sentiment analysis project"""
    
    def __init__(self, cloud_path=None):
        """
        Initialize data syncer
        
        Args:
            cloud_path: Path to cloud storage (Google Drive, Dropbox, OneDrive, etc.)
        """
        self.project_root = Path.cwd()
        self.data_dirs = [
            "data/raw",
            "data/processed", 
            "data/FinBERT_data",
            "data/llama3_data",
            "data/comparison",
            "data/results",
            "logs"
        ]
        
        # Cloud storage path (customize for your setup)
        if cloud_path:
            self.cloud_path = Path(cloud_path)
        else:
            self.cloud_path = self._detect_cloud_storage()
        
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Cloud storage: {self.cloud_path}")
    
    def _detect_cloud_storage(self):
        """Try to detect common cloud storage locations"""
        possible_paths = [
            # Google Drive
            Path.home() / "Google Drive" / "trump-sentiment-data",
            Path.home() / "GoogleDrive" / "trump-sentiment-data",
            
            # Dropbox
            Path.home() / "Dropbox" / "trump-sentiment-data",
            
            # OneDrive
            Path.home() / "OneDrive" / "trump-sentiment-data",
            
            # iCloud Drive (Mac)
            Path.home() / "Library" / "Mobile Documents" / "com~apple~CloudDocs" / "trump-sentiment-data",
            
            # Custom folder in Documents
            Path.home() / "Documents" / "trump-sentiment-data-sync"
        ]
        
        for path in possible_paths:
            if path.parent.exists():
                return path
        
        # Default fallback
        return Path.home() / "Documents" / "trump-sentiment-data-sync"
    
    def backup_data(self):
        """Backup data to cloud storage"""
        logger.info("Starting data backup...")
        
        # Create cloud directory if it doesn't exist
        self.cloud_path.mkdir(parents=True, exist_ok=True)
        
        # Create backup metadata
        backup_info = {
            "backup_date": datetime.now().isoformat(),
            "project_path": str(self.project_root),
            "backed_up_dirs": []
        }
        
        total_files = 0
        total_size = 0
        
        for data_dir in self.data_dirs:
            source_dir = self.project_root / data_dir
            target_dir = self.cloud_path / data_dir
            
            if source_dir.exists():
                logger.info(f"Backing up {data_dir}...")
                
                # Create target directory
                target_dir.mkdir(parents=True, exist_ok=True)
                
                # Copy files
                files_copied = 0
                dir_size = 0
                
                for file_path in source_dir.rglob('*'):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(source_dir)
                        target_file = target_dir / relative_path
                        
                        # Create parent directories
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Copy file
                        shutil.copy2(file_path, target_file)
                        files_copied += 1
                        dir_size += file_path.stat().st_size
                
                backup_info["backed_up_dirs"].append({
                    "directory": data_dir,
                    "files": files_copied,
                    "size_mb": round(dir_size / (1024*1024), 2)
                })
                
                total_files += files_copied
                total_size += dir_size
                
                logger.info(f"  ✅ {files_copied} files, {dir_size/(1024*1024):.1f} MB")
            else:
                logger.info(f"  ⏩ {data_dir} doesn't exist, skipping")
        
        # Save backup metadata
        with open(self.cloud_path / "backup_info.json", 'w') as f:
            json.dump(backup_info, f, indent=2)
        
        logger.info(f"✅ Backup completed!")
        logger.info(f"📊 Total: {total_files} files, {total_size/(1024*1024):.1f} MB")
        logger.info(f"📁 Backup location: {self.cloud_path}")
        
        return backup_info
    
    def restore_data(self):
        """Restore data from cloud storage"""
        logger.info("Starting data restore...")
        
        if not self.cloud_path.exists():
            logger.error(f"Cloud storage path not found: {self.cloud_path}")
            return False
        
        # Check for backup metadata
        backup_info_file = self.cloud_path / "backup_info.json"
        if backup_info_file.exists():
            with open(backup_info_file, 'r') as f:
                backup_info = json.load(f)
            logger.info(f"Found backup from: {backup_info['backup_date']}")
        else:
            logger.warning("No backup metadata found, proceeding anyway...")
            backup_info = None
        
        total_files = 0
        total_size = 0
        
        for data_dir in self.data_dirs:
            source_dir = self.cloud_path / data_dir
            target_dir = self.project_root / data_dir
            
            if source_dir.exists():
                logger.info(f"Restoring {data_dir}...")
                
                # Create target directory
                target_dir.mkdir(parents=True, exist_ok=True)
                
                # Copy files
                files_copied = 0
                dir_size = 0
                
                for file_path in source_dir.rglob('*'):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(source_dir)
                        target_file = target_dir / relative_path
                        
                        # Create parent directories
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Copy file
                        shutil.copy2(file_path, target_file)
                        files_copied += 1
                        dir_size += file_path.stat().st_size
                
                total_files += files_copied
                total_size += dir_size
                
                logger.info(f"  ✅ {files_copied} files, {dir_size/(1024*1024):.1f} MB")
            else:
                logger.info(f"  ⏩ {data_dir} not in backup, skipping")
        
        logger.info(f"✅ Restore completed!")
        logger.info(f"📊 Total: {total_files} files, {total_size/(1024*1024):.1f} MB")
        
        return True
    
    def check_status(self):
        """Check data status on both local and cloud"""
        logger.info("Checking data status...")
        
        def analyze_directory(path, label):
            if not path.exists():
                logger.info(f"{label}: Directory doesn't exist")
                return {"exists": False}
            
            total_files = 0
            total_size = 0
            by_type = {}
            
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    total_files += 1
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    
                    ext = file_path.suffix.lower()
                    if ext not in by_type:
                        by_type[ext] = {"count": 0, "size": 0}
                    by_type[ext]["count"] += 1
                    by_type[ext]["size"] += file_size
            
            logger.info(f"{label}: {total_files} files, {total_size/(1024*1024):.1f} MB")
            
            # Show top file types
            if by_type:
                sorted_types = sorted(by_type.items(), key=lambda x: x[1]["size"], reverse=True)
                for ext, info in sorted_types[:5]:
                    ext_name = ext if ext else "no extension"
                    logger.info(f"  {ext_name}: {info['count']} files, {info['size']/(1024*1024):.1f} MB")
            
            return {
                "exists": True,
                "total_files": total_files,
                "total_size_mb": round(total_size / (1024*1024), 2),
                "file_types": by_type
            }
        
        # Analyze local data
        logger.info("\n📁 LOCAL DATA:")
        local_data = self.project_root / "data"
        local_status = analyze_directory(local_data, "Local")
        
        # Analyze cloud data
        logger.info("\n☁️  CLOUD DATA:")
        cloud_status = analyze_directory(self.cloud_path, "Cloud")
        
        # Check specific important files
        important_files = [
            "data/processed/market_relevant_posts.csv",
            "data/FinBERT_data/finbert_sentiment_results.csv"
        ]
        
        logger.info("\n🔍 IMPORTANT FILES:")
        for file_path in important_files:
            local_file = self.project_root / file_path
            cloud_file = self.cloud_path / file_path
            
            local_exists = "✅" if local_file.exists() else "❌"
            cloud_exists = "✅" if cloud_file.exists() else "❌"
            
            logger.info(f"  {file_path}")
            logger.info(f"    Local: {local_exists}  Cloud: {cloud_exists}")
        
        return {"local": local_status, "cloud": cloud_status}

def main():
    parser = argparse.ArgumentParser(description='Sync data for sentiment analysis project')
    parser.add_argument('--backup', action='store_true', help='Backup data to cloud storage')
    parser.add_argument('--restore', action='store_true', help='Restore data from cloud storage')
    parser.add_argument('--status', action='store_true', help='Check data status')
    parser.add_argument('--cloud-path', type=str, help='Custom cloud storage path')
    
    args = parser.parse_args()
    
    if not any([args.backup, args.restore, args.status]):
        parser.print_help()
        return
    
    syncer = DataSyncer(args.cloud_path)
    
    if args.backup:
        syncer.backup_data()
    elif args.restore:
        syncer.restore_data()
    elif args.status:
        syncer.check_status()

if __name__ == "__main__":
    main() 