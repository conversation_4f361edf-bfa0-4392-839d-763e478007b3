#!/usr/bin/env python3
"""
Complete Sentiment Analysis Comparison Pipeline
Runs FinBERT and Llama 3.1 sentiment analysis on market-relevant posts,
then performs comprehensive comparison analysis.

Usage:
    python run_sentiment_comparison.py [--subset N] [--skip-llama3] [--skip-comparison]
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add src to path for imports
sys.path.append('src')

from analysis.llama3_sentiment import Llama3SentimentAnalyzer, load_market_posts, save_results
from analysis.model_comparison import ModelComparisonFramework

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/sentiment_comparison.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if all required files and dependencies are available"""
    logger.info("Checking requirements...")
    
    # Check required files
    required_files = [
        "data/processed/market_relevant_posts.csv",
        "data/FinBERT_data/finbert_sentiment_results.csv"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error("Missing required files:")
        for file_path in missing_files:
            logger.error(f"  - {file_path}")
        return False
    
    # Check required directories
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data/llama3_data", exist_ok=True)
    os.makedirs("data/comparison", exist_ok=True)
    
    logger.info("✅ All requirements satisfied")
    return True

def run_llama3_analysis(subset_size=None):
    """Run Llama 3.1 sentiment analysis"""
    logger.info("=" * 60)
    logger.info("RUNNING LLAMA 3.1 SENTIMENT ANALYSIS")
    logger.info("=" * 60)
    
    try:
        # Load market-relevant posts
        input_file = "data/processed/market_relevant_posts.csv"
        df_posts = load_market_posts(input_file)
        
        # Apply subset if requested
        if subset_size and subset_size < len(df_posts):
            logger.info(f"Using subset of {subset_size} posts for testing")
            df_posts = df_posts.head(subset_size)
        
        # Initialize Llama 3.1 analyzer
        logger.info("Initializing Llama 3.1 model...")
        analyzer = Llama3SentimentAnalyzer()
        
        # Analyze sentiment
        logger.info(f"Starting sentiment analysis on {len(df_posts)} posts...")
        start_time = time.time()
        
        texts = df_posts['text'].tolist()
        sentiment_results = analyzer.analyze_batch(texts)
        
        analysis_time = time.time() - start_time
        logger.info(f"Analysis completed in {analysis_time:.1f} seconds")
        
        # Prepare results DataFrame
        logger.info("Preparing results...")
        df_results = df_posts.copy()
        
        # Add sentiment columns
        df_results['primary_sentiment'] = [r['primary_sentiment'] for r in sentiment_results]
        df_results['primary_score'] = [r['primary_score'] for r in sentiment_results]
        df_results['positive_score'] = [r['positive_score'] for r in sentiment_results]
        df_results['negative_score'] = [r['negative_score'] for r in sentiment_results]
        df_results['neutral_score'] = [r['neutral_score'] for r in sentiment_results]
        df_results['processing_time'] = [r['processing_time'] for r in sentiment_results]
        df_results['processed_text'] = [r['text'] for r in sentiment_results]
        
        # Save results
        output_dir = "data/llama3_data"
        csv_path, json_path, summary_path = save_results(df_results, output_dir)
        
        # Print summary
        logger.info("✅ Llama 3.1 sentiment analysis completed!")
        logger.info(f"📊 Processed {len(df_results)} posts")
        
        # Print sentiment distribution
        sentiment_dist = df_results['primary_sentiment'].value_counts()
        logger.info("📈 Sentiment Distribution:")
        for sentiment, count in sentiment_dist.items():
            percentage = (count / len(df_results)) * 100
            logger.info(f"  {sentiment.capitalize()}: {count} posts ({percentage:.1f}%)")
        
        # Print average scores
        logger.info("📊 Average Sentiment Scores:")
        logger.info(f"  Positive: {df_results['positive_score'].mean():.3f}")
        logger.info(f"  Negative: {df_results['negative_score'].mean():.3f}")
        logger.info(f"  Neutral: {df_results['neutral_score'].mean():.3f}")
        
        # Print performance stats
        total_time = df_results['processing_time'].sum()
        avg_time = df_results['processing_time'].mean()
        logger.info(f"⏱️  Processing Performance:")
        logger.info(f"  Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        logger.info(f"  Average per post: {avg_time:.2f} seconds")
        
        logger.info(f"📁 Results saved to:")
        logger.info(f"  CSV: {csv_path}")
        logger.info(f"  JSON: {json_path}")
        logger.info(f"  Summary: {summary_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in Llama 3.1 analysis: {e}")
        return False

def run_model_comparison():
    """Run comprehensive model comparison analysis"""
    logger.info("=" * 60)
    logger.info("RUNNING MODEL COMPARISON ANALYSIS")
    logger.info("=" * 60)
    
    try:
        # File paths
        finbert_results = "data/FinBERT_data/finbert_sentiment_results.csv"
        llama3_results = "data/llama3_data/llama3_sentiment_results.csv"
        output_dir = "data/comparison"
        
        # Check if Llama 3.1 results exist
        if not os.path.exists(llama3_results):
            logger.error(f"Llama 3.1 results not found: {llama3_results}")
            logger.error("Run Llama 3.1 analysis first or use --skip-comparison flag")
            return False
        
        # Initialize comparison framework
        comparison = ModelComparisonFramework(finbert_results, llama3_results, output_dir)
        
        # Load results
        if not comparison.load_results():
            logger.error("Failed to load model results")
            return False
        
        # Generate comprehensive comparison report
        logger.info("Generating comprehensive comparison report...")
        results = comparison.generate_comparison_report()
        
        # Print summary
        logger.info("✅ Model comparison completed!")
        logger.info(f"📊 Compared {len(comparison.df_comparison)} posts")
        
        agreement_rate = results['report']['agreement_analysis']['label_agreement']
        logger.info(f"🤝 Overall Agreement: {agreement_rate:.1%}")
        
        disagreement_rate = results['report']['disagreement_analysis']['disagreement_rate']
        logger.info(f"🔄 Disagreement Rate: {disagreement_rate:.1%}")
        
        # Print correlations
        correlations = results['report']['agreement_analysis']['score_correlations']
        logger.info("📈 Score Correlations (Pearson):")
        for sentiment in ['positive', 'negative', 'neutral']:
            corr = correlations[sentiment]['pearson']
            logger.info(f"  {sentiment.capitalize()}: {corr:.3f}")
        
        # Print top insights
        logger.info("💡 Key Insights:")
        for insight in results['report']['summary_insights']:
            logger.info(f"  • {insight}")
        
        # Print disagreement patterns
        patterns = results['report']['disagreement_analysis']['disagreement_patterns']
        if patterns:
            logger.info("🔄 Top Disagreement Patterns:")
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:3]
            for pattern, count in sorted_patterns:
                logger.info(f"  {pattern.replace('_to_', ' → ')}: {count} cases")
        
        # Print sector analysis
        if 'sector_analysis' in results['report']:
            logger.info("🏭 Sector Agreement Rates:")
            sector_data = results['report']['sector_analysis']
            sorted_sectors = sorted(sector_data.items(), 
                                  key=lambda x: x[1]['agreement_rate'], reverse=True)
            for sector, data in sorted_sectors[:5]:
                logger.info(f"  {sector.capitalize()}: {data['agreement_rate']:.1%} "
                           f"({data['post_count']} posts)")
        
        logger.info("📁 Output files:")
        for file_type, path in results['files'].items():
            logger.info(f"  {file_type.replace('_', ' ').title()}: {path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in comparison analysis: {e}")
        return False

def print_final_summary(llama3_success, comparison_success, start_time):
    """Print final pipeline summary"""
    total_time = time.time() - start_time
    
    logger.info("=" * 60)
    logger.info("PIPELINE SUMMARY")
    logger.info("=" * 60)
    
    logger.info(f"⏱️  Total execution time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
    
    logger.info("📋 Task Status:")
    logger.info(f"  Llama 3.1 Analysis: {'✅ SUCCESS' if llama3_success else '❌ FAILED'}")
    logger.info(f"  Model Comparison: {'✅ SUCCESS' if comparison_success else '❌ FAILED'}")
    
    if llama3_success and comparison_success:
        logger.info("\n🎉 Complete sentiment analysis comparison pipeline finished successfully!")
        logger.info("\n📁 Key Output Files:")
        logger.info("  • data/llama3_data/llama3_sentiment_results.csv - Llama 3.1 sentiment results")
        logger.info("  • data/comparison/detailed_comparison_results.csv - Side-by-side comparison")
        logger.info("  • data/comparison/comparison_report.json - Comprehensive analysis report")
        logger.info("  • data/comparison/model_comparison_visualizations.png - Comparison charts")
        logger.info("  • data/comparison/comparison_summary.txt - Human-readable summary")
        
        logger.info("\n📈 Next Steps:")
        logger.info("  1. Review the comparison visualizations")
        logger.info("  2. Examine disagreement cases for insights")
        logger.info("  3. Consider sector-specific performance differences")
        logger.info("  4. Evaluate which model better suits your use case")
    
    elif llama3_success:
        logger.info("\n✅ Llama 3.1 analysis completed successfully!")
        logger.info("❗ Model comparison skipped or failed")
    
    else:
        logger.info("\n❌ Pipeline failed. Check logs for detailed error information.")

def main():
    """Main pipeline execution"""
    parser = argparse.ArgumentParser(description='Run sentiment analysis comparison pipeline')
    parser.add_argument('--subset', type=int, help='Use subset of N posts for testing')
    parser.add_argument('--skip-llama3', action='store_true', 
                       help='Skip Llama 3 analysis (use existing results)')
    parser.add_argument('--skip-comparison', action='store_true',
                       help='Skip model comparison analysis')
    parser.add_argument('--comparison-only', action='store_true',
                       help='Only run comparison (skip Llama 3 analysis)')
    
    args = parser.parse_args()
    
    # Set flags
    skip_llama3 = args.skip_llama3 or args.comparison_only
    skip_comparison = args.skip_comparison
    
    logger.info("🚀 Starting Sentiment Analysis Comparison Pipeline")
    logger.info(f"Configuration: subset={args.subset}, skip_llama3={skip_llama3}, skip_comparison={skip_comparison}")
    
    start_time = time.time()
    
    # Check requirements
    if not check_requirements():
        logger.error("Requirements check failed. Exiting.")
        sys.exit(1)
    
    # Execute pipeline
    llama3_success = True
    comparison_success = True
    
    if not skip_llama3:
        llama3_success = run_llama3_analysis(args.subset)
        if not llama3_success:
            logger.error("Llama 3.1 analysis failed. Stopping pipeline.")
            print_final_summary(llama3_success, False, start_time)
            sys.exit(1)
    else:
        logger.info("⏩ Skipping Llama 3.1 analysis (using existing results)")
    
    if not skip_comparison:
        comparison_success = run_model_comparison()
    else:
        logger.info("⏩ Skipping model comparison analysis")
    
    # Print final summary
    print_final_summary(llama3_success, comparison_success, start_time)
    
    # Exit with appropriate code
    if llama3_success and comparison_success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main() 