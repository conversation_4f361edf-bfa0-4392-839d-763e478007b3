#!/usr/bin/env python3
"""
Llama 3.1 Sentiment Analysis for Market-Relevant Posts
Uses Llama 3.1 instruction-following model for prompt-based sentiment classification
to analyze sentiment of <PERSON>'s market-relevant posts for comparison with FinBERT
"""

import pandas as pd
import numpy as np
import json
import logging
import re
import time
from datetime import datetime
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Import transformers for Llama 3.1
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from huggingface_hub import login
import torch

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/llama3_sentiment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Llama3SentimentAnalyzer:
    """
    Llama 3.1 sentiment analyzer using prompt-based classification for financial text
    """
    
    def __init__(self, model_name="meta-llama/Meta-Llama-3.1-8B-Instruct"):
        """
        Initialize Llama 3.1 model for sentiment analysis
        
        Args:
            model_name: HuggingFace model identifier for Llama 3.1
        """
        logger.info("Initializing Llama 3.1 Sentiment Analyzer")
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Initialize model and tokenizer
        try:
            logger.info(f"Loading model: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name,
                trust_remote_code=True
            )
            
            # Set pad token if not exists
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True
            )
            
            # Create text generation pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                do_sample=False,  # Deterministic for consistency
                temperature=0.1,
                max_new_tokens=50,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            logger.info("Llama 3.1 model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading Llama 3.1 model: {e}")
            logger.error("Make sure you have access to the model and are logged in to Hugging Face")
            logger.error("Run: huggingface-cli login")
            raise
    
    def _create_sentiment_prompt(self, text):
        """
        Create a structured prompt for sentiment analysis
        
        Args:
            text: Input text to analyze
            
        Returns:
            str: Formatted prompt for sentiment classification
        """
        # Clean text for prompt
        cleaned_text = self.preprocess_text(text)
        
        prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a financial sentiment analysis expert. Analyze the sentiment of financial and political text that may impact markets. Classify the sentiment as exactly one of: POSITIVE, NEGATIVE, or NEUTRAL.

For each classification, also provide a confidence score from 0.0 to 1.0.

Respond ONLY in this exact format:
SENTIMENT: [POSITIVE/NEGATIVE/NEUTRAL]
CONFIDENCE: [0.0-1.0]
POSITIVE_SCORE: [0.0-1.0]
NEGATIVE_SCORE: [0.0-1.0]
NEUTRAL_SCORE: [0.0-1.0]<|eot_id|><|start_header_id|>user<|end_header_id|>

Analyze the sentiment of this text regarding its potential market impact:

"{cleaned_text}"<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        return prompt
    
    def _parse_llama_response(self, response_text):
        """
        Parse Llama 3 response to extract sentiment scores
        
        Args:
            response_text: Raw response from Llama 3
            
        Returns:
            dict: Parsed sentiment scores and labels
        """
        try:
            # Extract the assistant's response part
            if "<|start_header_id|>assistant<|end_header_id|>" in response_text:
                response_part = response_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1]
            else:
                response_part = response_text
            
            # Initialize default values
            sentiment_data = {
                'primary_sentiment': 'neutral',
                'primary_score': 0.33,
                'positive_score': 0.33,
                'negative_score': 0.33,
                'neutral_score': 0.34
            }
            
            # Parse sentiment
            sentiment_match = re.search(r'SENTIMENT:\s*(POSITIVE|NEGATIVE|NEUTRAL)', response_part, re.IGNORECASE)
            if sentiment_match:
                sentiment_data['primary_sentiment'] = sentiment_match.group(1).lower()
            
            # Parse confidence
            confidence_match = re.search(r'CONFIDENCE:\s*([0-9.]+)', response_part)
            if confidence_match:
                sentiment_data['primary_score'] = float(confidence_match.group(1))
            
            # Parse individual scores
            pos_match = re.search(r'POSITIVE_SCORE:\s*([0-9.]+)', response_part)
            if pos_match:
                sentiment_data['positive_score'] = float(pos_match.group(1))
            
            neg_match = re.search(r'NEGATIVE_SCORE:\s*([0-9.]+)', response_part)
            if neg_match:
                sentiment_data['negative_score'] = float(neg_match.group(1))
            
            neut_match = re.search(r'NEUTRAL_SCORE:\s*([0-9.]+)', response_part)
            if neut_match:
                sentiment_data['neutral_score'] = float(neut_match.group(1))
            
            # Normalize scores if they don't sum to 1
            total = sentiment_data['positive_score'] + sentiment_data['negative_score'] + sentiment_data['neutral_score']
            if total > 0 and abs(total - 1.0) > 0.1:
                sentiment_data['positive_score'] /= total
                sentiment_data['negative_score'] /= total
                sentiment_data['neutral_score'] /= total
            
            return sentiment_data
            
        except Exception as e:
            logger.warning(f"Error parsing Llama response: {e}")
            return {
                'primary_sentiment': 'neutral',
                'primary_score': 0.33,
                'positive_score': 0.33,
                'negative_score': 0.33,
                'neutral_score': 0.34
            }
    
    def analyze_text(self, text):
        """
        Analyze sentiment of a single text using Llama 3.1
        
        Args:
            text: Input text to analyze
            
        Returns:
            dict: Sentiment analysis results with scores and labels
        """
        try:
            # Create prompt
            prompt = self._create_sentiment_prompt(text)
            
            # Generate response
            start_time = time.time()
            response = self.pipeline(
                prompt,
                max_new_tokens=50,
                do_sample=False,
                temperature=0.1,
                pad_token_id=self.tokenizer.eos_token_id
            )
            processing_time = time.time() - start_time
            
            # Extract generated text
            generated_text = response[0]['generated_text']
            
            # Parse sentiment from response
            sentiment_data = self._parse_llama_response(generated_text)
            
            # Create sentiment scores dict for consistency with FinBERT
            sentiment_scores = {
                'positive': sentiment_data['positive_score'],
                'negative': sentiment_data['negative_score'],
                'neutral': sentiment_data['neutral_score']
            }
            
            return {
                'text': self.preprocess_text(text),
                'sentiment_scores': sentiment_scores,
                'primary_sentiment': sentiment_data['primary_sentiment'],
                'primary_score': sentiment_data['primary_score'],
                'positive_score': sentiment_data['positive_score'],
                'negative_score': sentiment_data['negative_score'],
                'neutral_score': sentiment_data['neutral_score'],
                'processing_time': processing_time,
                'raw_response': generated_text
            }
            
        except Exception as e:
            logger.error(f"Error analyzing text with Llama 3.1: {e}")
            return {
                'text': text,
                'sentiment_scores': {'positive': 0.0, 'negative': 0.0, 'neutral': 1.0},
                'primary_sentiment': 'neutral',
                'primary_score': 1.0,
                'positive_score': 0.0,
                'negative_score': 0.0,
                'neutral_score': 1.0,
                'processing_time': 0.0,
                'error': str(e)
            }
    
    def preprocess_text(self, text):
        """
        Clean and preprocess text for sentiment analysis
        
        Args:
            text: Raw text
            
        Returns:
            str: Cleaned text
        """
        if not isinstance(text, str):
            text = str(text)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Truncate if too long to prevent token limit issues
        max_chars = 1000  # Conservative limit for prompt-based approach
        if len(text) > max_chars:
            text = text[:max_chars] + "..."
            logger.warning(f"Text truncated to {max_chars} characters")
        
        return text
    
    def analyze_batch(self, texts, batch_size=1):
        """
        Analyze sentiment for a batch of texts
        Note: Llama 3.1 processes one at a time due to generation approach
        
        Args:
            texts: List of texts to analyze
            batch_size: Ignored for Llama 3.1 (processes sequentially)
            
        Returns:
            list: List of sentiment analysis results
        """
        results = []
        total = len(texts)
        
        logger.info(f"Processing {total} texts with Llama 3.1 (sequential processing)")
        
        for i, text in enumerate(texts):
            logger.info(f"Processing text {i + 1}/{total}")
            result = self.analyze_text(text)
            results.append(result)
            
            # Log progress every 10 items
            if (i + 1) % 10 == 0 or (i + 1) == total:
                avg_time = sum(r.get('processing_time', 0) for r in results) / len(results)
                logger.info(f"Completed {i + 1}/{total} texts (avg: {avg_time:.2f}s per text)")
        
        return results

def load_market_posts(file_path):
    """
    Load market-relevant posts from CSV file
    
    Args:
        file_path: Path to CSV file
        
    Returns:
        pandas.DataFrame: Loaded posts data
    """
    try:
        logger.info(f"Loading market posts from: {file_path}")
        df = pd.read_csv(file_path)
        logger.info(f"Loaded {len(df)} market-relevant posts")
        return df
    except Exception as e:
        logger.error(f"Error loading posts: {e}")
        raise

def save_results(df_results, output_dir):
    """
    Save Llama 3.1 sentiment analysis results to multiple formats
    
    Args:
        df_results: DataFrame with sentiment results
        output_dir: Output directory path
    """
    try:
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Save detailed results as CSV
        csv_path = os.path.join(output_dir, "llama3_sentiment_results.csv")
        df_results.to_csv(csv_path, index=False)
        logger.info(f"Saved detailed results to: {csv_path}")
        
        # Save as JSON for easier programmatic access
        json_path = os.path.join(output_dir, "llama3_sentiment_results.json")
        df_results.to_json(json_path, orient='records', indent=2, date_format='iso')
        logger.info(f"Saved JSON results to: {json_path}")
        
        # Create summary statistics
        summary = create_summary_stats(df_results)
        summary_path = os.path.join(output_dir, "llama3_sentiment_summary.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        logger.info(f"Saved summary to: {summary_path}")
        
        return csv_path, json_path, summary_path
        
    except Exception as e:
        logger.error(f"Error saving results: {e}")
        raise

def create_summary_stats(df):
    """
    Create summary statistics for Llama 3.1 sentiment analysis results
    
    Args:
        df: DataFrame with sentiment results
        
    Returns:
        dict: Summary statistics
    """
    summary = {
        'analysis_timestamp': datetime.now().isoformat(),
        'model_name': 'meta-llama/Meta-Llama-3.1-8B-Instruct',
        'total_posts': len(df),
        'sentiment_distribution': {
            'positive': int((df['primary_sentiment'] == 'positive').sum()),
            'negative': int((df['primary_sentiment'] == 'negative').sum()),
            'neutral': int((df['primary_sentiment'] == 'neutral').sum())
        },
        'average_scores': {
            'positive': float(df['positive_score'].mean()),
            'negative': float(df['negative_score'].mean()),
            'neutral': float(df['neutral_score'].mean())
        },
        'score_statistics': {
            'positive': {
                'mean': float(df['positive_score'].mean()),
                'std': float(df['positive_score'].std()),
                'min': float(df['positive_score'].min()),
                'max': float(df['positive_score'].max())
            },
            'negative': {
                'mean': float(df['negative_score'].mean()),
                'std': float(df['negative_score'].std()),
                'min': float(df['negative_score'].min()),
                'max': float(df['negative_score'].max())
            },
            'neutral': {
                'mean': float(df['neutral_score'].mean()),
                'std': float(df['neutral_score'].std()),
                'min': float(df['neutral_score'].min()),
                'max': float(df['neutral_score'].max())
            }
        },
        'performance_metrics': {
            'average_processing_time': float(df['processing_time'].mean()) if 'processing_time' in df.columns else None,
            'total_processing_time': float(df['processing_time'].sum()) if 'processing_time' in df.columns else None
        }
    }
    
    return summary

def main():
    """
    Main function to run Llama 3.1 sentiment analysis
    """
    logger.info("Starting Llama 3.1 sentiment analysis")
    
    # File paths
    input_file = "data/processed/market_relevant_posts.csv"
    output_dir = "data/llama3_data"
    
    try:
        # Load market-relevant posts
        df_posts = load_market_posts(input_file)
        
        # Initialize Llama 3.1 analyzer
        analyzer = Llama3SentimentAnalyzer()
        
        # Analyze sentiment
        logger.info("Starting sentiment analysis...")
        texts = df_posts['text'].tolist()
        sentiment_results = analyzer.analyze_batch(texts)
        
        # Prepare results DataFrame
        logger.info("Preparing results...")
        
        # Create new DataFrame with original data + sentiment
        df_results = df_posts.copy()
        
        # Add sentiment columns
        df_results['primary_sentiment'] = [r['primary_sentiment'] for r in sentiment_results]
        df_results['primary_score'] = [r['primary_score'] for r in sentiment_results]
        df_results['positive_score'] = [r['positive_score'] for r in sentiment_results]
        df_results['negative_score'] = [r['negative_score'] for r in sentiment_results]
        df_results['neutral_score'] = [r['neutral_score'] for r in sentiment_results]
        df_results['processing_time'] = [r['processing_time'] for r in sentiment_results]
        
        # Add processed text
        df_results['processed_text'] = [r['text'] for r in sentiment_results]
        
        # Save results
        csv_path, json_path, summary_path = save_results(df_results, output_dir)
        
        # Print summary
        logger.info("✅ Llama 3.1 sentiment analysis completed!")
        logger.info(f"📊 Processed {len(df_results)} posts")
        
        # Print sentiment distribution
        sentiment_dist = df_results['primary_sentiment'].value_counts()
        logger.info("📈 Sentiment Distribution:")
        for sentiment, count in sentiment_dist.items():
            percentage = (count / len(df_results)) * 100
            logger.info(f"  {sentiment.capitalize()}: {count} posts ({percentage:.1f}%)")
        
        # Print average scores
        logger.info("📊 Average Sentiment Scores:")
        logger.info(f"  Positive: {df_results['positive_score'].mean():.3f}")
        logger.info(f"  Negative: {df_results['negative_score'].mean():.3f}")
        logger.info(f"  Neutral: {df_results['neutral_score'].mean():.3f}")
        
        # Print performance stats
        total_time = df_results['processing_time'].sum()
        avg_time = df_results['processing_time'].mean()
        logger.info(f"⏱️  Processing Performance:")
        logger.info(f"  Total time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
        logger.info(f"  Average per post: {avg_time:.2f} seconds")
        
        logger.info(f"📁 Results saved to:")
        logger.info(f"  CSV: {csv_path}")
        logger.info(f"  JSON: {json_path}")
        logger.info(f"  Summary: {summary_path}")
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main() 