# Llama 3.1 Access Guide

This guide provides step-by-step instructions for gaining access to Meta's Llama 3.1 models on Hugging Face and setting up authentication for the sentiment analysis comparison framework.

## Overview

Meta's Llama 3.1 models are **gated models** on Hugging Face, which means you need:
1. ✅ A Hugging Face account
2. ✅ Approval from Meta to access Llama 3.1
3. ✅ A Hugging Face access token
4. ✅ Proper authentication setup

## Step 1: Create Hugging Face Account

If you don't already have one:

1. Go to [https://huggingface.co/join](https://huggingface.co/join)
2. Sign up with your email address
3. Verify your email address
4. Complete your profile (recommended)

## Step 2: Request Access to Llama 3.1

### 2.1 Visit the Model Page
Go to the Llama 3.1 model you want to use:
- **8B Model**: [meta-llama/Meta-Llama-3.1-8B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-8B-Instruct)
- **70B Model**: [meta-llama/Meta-Llama-3.1-70B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-70B-Instruct)
- **405B Model**: [meta-llama/Meta-Llama-3.1-405B-Instruct](https://huggingface.co/meta-llama/Meta-Llama-3.1-405B-Instruct)

### 2.2 Request Access
1. **Click "Request access"** button on the model page
2. **Fill out the form** with:
   - Your intended use case (research/commercial)
   - Organization details (if applicable)
   - Contact information
3. **Agree to the license terms**
4. **Submit the request**

### 2.3 Wait for Approval
- **Approval time**: Usually 1-3 business days
- **Email notification**: You'll receive an email when approved
- **Status check**: Visit the model page to see if access is granted

> **Note**: You need to request access for each model size separately (8B, 70B, 405B).

## Step 3: Create Access Token

### 3.1 Generate Token
1. Go to [https://huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. Click **"New token"**
3. Choose token type:
   - **Read**: For downloading models (sufficient for this project)
   - **Write**: Only if you plan to upload models
4. **Name your token** (e.g., "llama31-sentiment-analysis")
5. Click **"Generate a token"**
6. **Copy the token** immediately (you won't see it again!)

### 3.2 Token Security
⚠️ **Important**: Keep your token secure!
- Don't share it publicly
- Don't commit it to version control
- Store it safely (password manager recommended)

## Step 4: Authentication Setup

Choose one of the following methods:

### Method 1: Command Line Login (Recommended)

```bash
# Install huggingface_hub if not already installed
pip install huggingface_hub

# Login using the CLI
huggingface-cli login
```

When prompted:
1. **Paste your token** (it won't be visible while typing)
2. **Choose**: Add token as git credential? (recommended: Y)

### Method 2: Environment Variable

```bash
# Set environment variable (Linux/Mac)
export HUGGINGFACE_HUB_TOKEN="your_token_here"

# Set environment variable (Windows)
set HUGGINGFACE_HUB_TOKEN=your_token_here
```

### Method 3: Python Code (Less Secure)

```python
from huggingface_hub import login

# Login programmatically
login(token="your_token_here")
```

### Method 4: Token File

Create a file `~/.cache/huggingface/token` with your token:
```bash
echo "your_token_here" > ~/.cache/huggingface/token
```

## Step 5: Verify Access

### Test Your Setup

```python
from transformers import AutoTokenizer

# Try to load the tokenizer (this will test your access)
try:
    tokenizer = AutoTokenizer.from_pretrained("meta-llama/Meta-Llama-3.1-8B-Instruct")
    print("✅ Access verified! You can use Llama 3.1 models.")
except Exception as e:
    print(f"❌ Access failed: {e}")
```

### Quick Test with Framework

```bash
# Test with our framework
python test_comparison_framework.py
```

## Step 6: Run the Sentiment Analysis

Once authenticated, you can run the comparison framework:

```bash
# Test with small subset first
python run_sentiment_comparison.py --subset 10

# Run full analysis
python run_sentiment_comparison.py
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "Repository not found" Error
```
Error: Repository meta-llama/Meta-Llama-3.1-8B-Instruct not found
```
**Solution**: You haven't been approved yet. Check your email and the model page.

#### 2. "Authentication required" Error
```
Error: You need to be authenticated to access this model
```
**Solutions**:
- Run `huggingface-cli login` again
- Check if your token is still valid
- Verify the token has the right permissions

#### 3. "Invalid token" Error
```
Error: Invalid authentication credentials
```
**Solutions**:
- Generate a new token from Hugging Face settings
- Make sure you copied the token correctly
- Re-run `huggingface-cli login`

#### 4. "Access denied" Error
```
Error: You don't have access to this model
```
**Solutions**:
- Request access on the model page
- Wait for approval (check email)
- Make sure you agreed to the license terms

#### 5. CUDA Out of Memory
```
Error: CUDA out of memory
```
**Solutions**:
```bash
# Use CPU-only mode
export CUDA_VISIBLE_DEVICES=""
python run_sentiment_comparison.py --subset 50

# Or use smaller model if needed
```

### Getting Help

If you encounter issues:

1. **Check Hugging Face Status**: [https://status.huggingface.co/](https://status.huggingface.co/)
2. **Hugging Face Forums**: [https://discuss.huggingface.co/](https://discuss.huggingface.co/)
3. **Framework Issues**: Check the logs in `logs/` directory

## Model Variants

### Available Llama 3.1 Models

| Model | Parameters | Memory (16-bit) | Use Case |
|-------|------------|-----------------|----------|
| `Meta-Llama-3.1-8B-Instruct` | 8B | ~16 GB | **Recommended** - Good balance |
| `Meta-Llama-3.1-70B-Instruct` | 70B | ~140 GB | High accuracy, requires large GPU |
| `Meta-Llama-3.1-405B-Instruct` | 405B | ~810 GB | Research use, multi-GPU setup |

### Choosing the Right Model

**For this sentiment analysis project**:
- **8B Model**: Recommended for most users (fits on single consumer GPU)
- **70B Model**: Better accuracy but requires high-end hardware
- **405B Model**: Research purposes only (requires multiple A100 GPUs)

## Hardware Requirements

### Minimum Requirements (8B Model)
- **RAM**: 32 GB system RAM
- **GPU**: 16 GB VRAM (RTX 4090, A4000, etc.)
- **Storage**: 50 GB free space

### Recommended Setup (8B Model)
- **RAM**: 64 GB system RAM
- **GPU**: 24 GB VRAM (RTX 4090, A5000, A6000)
- **Storage**: 100 GB SSD space

### CPU-Only Alternative
- **RAM**: 64 GB+ system RAM
- **Processing Time**: ~10x slower than GPU
- **Recommended**: Only for small subsets or testing

## Next Steps

After successful authentication:

1. **Test the setup**: `python test_comparison_framework.py`
2. **Start small**: `python run_sentiment_comparison.py --subset 10`
3. **Scale up**: `python run_sentiment_comparison.py --subset 50`
4. **Full analysis**: `python run_sentiment_comparison.py`
5. **Review results**: Check `data/comparison/` directory

## Security Best Practices

1. **Token Management**:
   - Use environment variables or CLI login
   - Never hardcode tokens in scripts
   - Regenerate tokens periodically

2. **Model Usage**:
   - Respect Meta's license terms
   - Don't redistribute model weights
   - Use for approved purposes only

3. **Data Privacy**:
   - Be aware of what data you send to models
   - Consider local inference for sensitive data
   - Follow your organization's data policies

## Support

For technical support:
- **Framework Issues**: Check logs and documentation
- **Authentication Issues**: Hugging Face support
- **Model Access**: Meta AI support through Hugging Face

---

**Last Updated**: January 2025
**Framework Version**: 1.0
**Llama Version**: 3.1 